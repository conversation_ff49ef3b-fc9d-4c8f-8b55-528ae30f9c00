import spawn from "nano-spawn";
import { Task, TaskLog, TaskExecutionResult } from "@/types/task";
import * as gitOperations from "./git-operations";
import * as jiraService from "@/services/jira";
import {
  REPO_PATH,
  NEXT_PUBLIC_APP_URL,
  AIDER_ARCHITECT,
  AIDER_MODEL,
  AIDER_EDITOR_MODEL,
  OPENAI_API_KEY,
  OPENAI_BASE_URL,
} from "@/config";

/**
 * Log a message with a specific type
 */
function logMessage(
  message: string,
  type: "info" | "error" | "command" = "info",
  logCallback: (log: Omit<TaskLog, "timestamp">) => void,
): void {
  logCallback({
    message,
    type,
  });
}

/**
 * Sanitize a string for safe use in command line arguments
 * Removes or escapes special shell characters
 */
function sanitizeCommandInput(input: string): string {
  // Remove YAML pipe symbol and newline that follows it
  let sanitized = input.replaceAll("|\n", "");

  // Remove other potentially problematic shell characters
  // or escape them as needed
  sanitized = sanitized.replaceAll(/[&;|<>$`"'\\]/g, "");

  return sanitized.trim();
}

/**
 * Run the aider AI agent
 */
async function runAider(
  taskDescription: string,
  repoPath: string,
  logCallback: (log: Omit<TaskLog, "timestamp">) => void,
): Promise<void> {
  try {
    // Sanitize the task description to avoid shell injection issues
    const sanitizedDescription = sanitizeCommandInput(taskDescription);

    const command = "aider";
    const arguments_ = [
      "--message",
      sanitizedDescription,
      "--no-auto-commits",
      "--yes",
      "--no-show-model-warnings",
      "--map-tokens",
      "0",
    ];
    const environment: Record<string, string> = {};

    // Add architect flag if enabled
    if (AIDER_ARCHITECT) {
      arguments_.push("--architect");
    }

    // Add model if specified
    if (AIDER_MODEL) {
      arguments_.push("--model", AIDER_MODEL);
    }

    // Add editor model if specified
    if (AIDER_EDITOR_MODEL) {
      arguments_.push("--editor-model", AIDER_EDITOR_MODEL);
    }

    // Log the original task description for reference
    logMessage(
      `Original task description: ${taskDescription}`,
      "info",
      logCallback,
    );

    // Log the sanitized command that will be executed
    logMessage(
      `Executing: ${command} ${arguments_.join(" ")}`,
      "command",
      logCallback,
    );

    const { stdout, stderr } = await spawn(command, arguments_, {
      cwd: repoPath,
      env: environment,
    });

    if (stderr) {
      logMessage(`aider stderr: ${stderr}`, "error", logCallback);
    }

    logMessage(`aider stdout: ${stdout}`, "info", logCallback);
  } catch (error) {
    const error_ = error as Error;
    logMessage(`Error running aider: ${error_.message}`, "error", logCallback);
    throw error;
  }
}

/**
 * Post a comment to Jira with the task URL
 */
async function postJiraComment(
  issueKey: string,
  commentId: string,
  logCallback: (log: Omit<TaskLog, "timestamp">) => void,
): Promise<void> {
  try {
    const baseUrl = NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    const taskUrl = `${baseUrl}/ai-tasks/${commentId}`;

    const commentBody = `Agent running, you can visit ${taskUrl} to get running information`;

    await jiraService.addComment(issueKey, commentBody);

    logMessage(`Posted comment to Jira issue ${issueKey}`, "info", logCallback);
  } catch (error) {
    const error_ = error as Error;
    logMessage(
      `Error posting Jira comment: ${error_.message}`,
      "error",
      logCallback,
    );
    // Don't throw here, as this is not critical to the task execution
  }
}

/**
 * Run the openhands AI agent
 */
async function runOpenHands(
  taskDescription: string,
  repoPath: string,
  logCallback: (log: Omit<TaskLog, "timestamp">) => void,
): Promise<void> {
  try {
    // Sanitize the task description to avoid shell injection issues
    const sanitizedDescription = sanitizeCommandInput(taskDescription);

    const timestamp = new Date()
      .toISOString()
      .replaceAll(/\D/g, "")
      .slice(0, 14);
    const containerName = `openhands-app-${timestamp}`;

    const command = "docker";
    const arguments_ = [
      "run",
      "-it",
      "--pull=always",
      `-e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik`,
      `-e SANDBOX_USER_ID=$(id -u)`,
      `-e SANDBOX_VOLUMES=${repoPath}:/workspace:rw`,
      `-e LLM_API_KEY=${OPENAI_API_KEY || "default-key"}`,
      `-e LLM_MODEL=ollama/qwen2.5`,
      `-e LLM_BASE_URL=${OPENAI_BASE_URL || "default-url"}`,
      `-e LOG_ALL_EVENTS=true`,
      `-v /var/run/docker.sock:/var/run/docker.sock`,
      `-v ~/.openhands-state:/.openhands-state`,
      `--add-host host.docker.internal:host-gateway`,
      `--name ${containerName}`,
      `docker.all-hands.dev/all-hands-ai/openhands:0.39`,
      `python -m openhands.core.main -t "${sanitizedDescription}"`,
    ];

    // Log the original task description for reference
    logMessage(
      `Original task description: ${taskDescription}`,
      "info",
      logCallback,
    );

    // Log the command that will be executed
    logMessage(
      `Executing: ${command} ${arguments_.join(" ")}`,
      "command",
      logCallback,
    );

    const { stdout, stderr } = await spawn(command, arguments_, {
      cwd: repoPath,
    });

    if (stderr) {
      logMessage(`openhands stderr: ${stderr}`, "error", logCallback);
    }

    logMessage(`openhands stdout: ${stdout}`, "info", logCallback);
  } catch (error) {
    const error_ = error as Error;
    logMessage(
      `Error running openhands: ${error_.message}`,
      "error",
      logCallback,
    );
    throw error;
  }
}

/**
 * Execute an AI task
 */
export async function execute(
  task: Task & { aiTool?: "aider" | "openhands" },
  logCallback: (log: Omit<TaskLog, "timestamp">) => void,
): Promise<TaskExecutionResult> {
  const repoPath = REPO_PATH;

  try {
    logMessage("Starting task execution", "info", logCallback);
    logMessage(`Task ID: ${task.id}`, "info", logCallback);
    logMessage(`Issue Key: ${task.issueKey}`, "info", logCallback);
    logMessage(`Version: ${task.version}`, "info", logCallback);

    // Step 1: Setup Git branches
    const gitLogCallback = (message: string) => {
      logMessage(message, "info", logCallback);
    };

    logMessage("Setting up Git branches", "command", logCallback);
    const branch = await gitOperations.setupForAITask(
      task.version,
      task.issueKey,
      task.commentId,
      repoPath,
      gitLogCallback,
    );

    // Step 2: Run AI agent based on task configuration
    logMessage(
      `Running AI agent with task description: ${task.description}`,
      "command",
      logCallback,
    );

    // Sanitize the description for logging purposes
    const sanitizedForLog = sanitizeCommandInput(task.description);
    if (sanitizedForLog !== task.description) {
      logMessage(
        `Task description contains special characters that will be sanitized`,
        "info",
        logCallback,
      );
    }

    // Determine which AI agent to run based on task configuration
    const useOpenHands =
      (task as Task & { aiTool?: "aider" | "openhands" }).aiTool ===
      "openhands";

    await (useOpenHands
      ? runOpenHands(task.description, repoPath, logCallback)
      : runAider(task.description, repoPath, logCallback));

    // Step 3: Commit and push changes
    logMessage("Committing and pushing changes", "command", logCallback);
    try {
      await gitOperations.commitAndPushChanges(
        task.issueKey,
        task.commentId,
        repoPath,
        gitLogCallback,
      );
    } catch (error) {
      const error_ = error as Error;
      logMessage(
        `Warning: Git commit/push failed: ${error_.message}`,
        "error",
        logCallback,
      );
      // Don't throw here, as this is not critical to the task execution
      // The AI task has completed successfully, git operations are just cleanup
    }

    // Step 4: Post comment to Jira
    logMessage("Posting comment to Jira", "command", logCallback);
    await postJiraComment(task.issueKey, task.commentId, logCallback);

    logMessage("Task execution completed successfully", "info", logCallback);

    return {
      success: true,
      logs: task.logs,
      branch,
    };
  } catch (error) {
    const error_ = error as Error;
    logMessage(
      `Task execution failed: ${error_.message}`,
      "error",
      logCallback,
    );

    return {
      success: false,
      logs: task.logs,
      error: error_.message,
    };
  }
}

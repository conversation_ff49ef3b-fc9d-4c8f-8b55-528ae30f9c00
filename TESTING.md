# Testing Setup with Vitest

This document describes the Vitest testing setup for pure functions in the Next.js project.

## Overview

We have successfully set up Vitest following the official documentation to test pure functions throughout the project. The setup includes comprehensive test coverage for utility functions with 100% code coverage.

## Setup Components

### 1. Dependencies

The following dependencies were added to support testing:

```json
{
  "devDependencies": {
    "vitest": "^3.1.4",
    "@vitest/ui": "^3.1.4",
    "@vitest/coverage-v8": "^3.1.4",
    "jsdom": "^26.1.0",
    "@testing-library/jest-dom": "^6.6.3",
    "vite": "^6.3.5"
  }
}
```

### 2. Configuration Files

- **`vitest.config.ts`**: Main Vitest configuration with TypeScript support
- **`src/__tests__/setup.ts`**: Test setup file with environment variables and global imports

### 3. Test Scripts

Added to `package.json`:

```json
{
  "scripts": {
    "test": "vitest",
    "test:run": "vitest run",
    "test:watch": "vitest --watch",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest run --coverage"
  }
}
```

## Pure Functions Tested

### Git Utilities (`src/utils/git-utils.ts`)

- `generateBranchName()` - Template-based branch name generation
- `generateBaseBranchName()` - Base branch name generation
- `generateIssueBranchName()` - Issue-specific branch name generation
- `generateTaskBranchName()` - Task-specific branch name generation
- `isValidBranchName()` - Git branch name validation
- `sanitizeBranchName()` - Branch name sanitization
- `extractVersion()` - Version extraction from fix versions

### String Utilities (`src/utils/string-utils.ts`)

- `sanitizeCommandInput()` - Command line input sanitization
- `generateTimestamp()` - Timestamp generation for containers
- `extractContentBetweenMarkers()` - Content extraction between delimiters
- `parseKeyValueLines()` - Key-value pair parsing
- `joinNonEmpty()` - Non-empty string joining
- `truncateText()` - Text truncation with ellipsis
- `camelToKebab()` - camelCase to kebab-case conversion
- `kebabToCamel()` - kebab-case to camelCase conversion
- `escapeRegex()` - Regular expression escaping
- `isValidUrl()` - URL validation

### Comment Parser (`src/lib/comment-parser.ts`)

- `parseComment()` - Jira comment parsing for AI tasks
- Internal helper functions for task block extraction and parsing

## Test Coverage

Current test coverage for utility functions:

```
File                | % Stmts | % Branch | % Funcs | % Lines
--------------------|---------|----------|---------|--------
git-utils.ts        |     100 |      100 |     100 |     100
string-utils.ts     |     100 |      100 |     100 |     100
comment-parser.ts   |   91.66 |    88.88 |     100 |   91.66
```

## Test Structure

### Test Organization

```
src/
├── __tests__/
│   ├── setup.ts                    # Test setup and configuration
│   ├── comment-parser.test.ts      # Comment parsing tests
│   ├── git-utils.test.ts          # Git utility function tests
│   └── string-utils.test.ts       # String utility function tests
├── utils/
│   ├── git-utils.ts               # Git utility functions
│   └── string-utils.ts            # String utility functions
└── lib/
    └── comment-parser.ts          # Comment parsing logic
```

### Test Patterns

1. **Comprehensive Edge Cases**: Tests cover normal cases, edge cases, and error conditions
2. **Pure Function Focus**: All tested functions are pure (no side effects)
3. **Mock Data Factories**: Reusable factory functions for creating test data
4. **Descriptive Test Names**: Clear, descriptive test names following BDD patterns

## Running Tests

### Basic Commands

```bash
# Run all tests once
pnpm test:run

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage

# Run tests with UI
pnpm test:ui
```

### Test Output

Tests provide detailed output including:

- Pass/fail status for each test
- Execution time
- Coverage reports
- Error details for failing tests

## ESLint Integration

ESLint is configured to handle test files with relaxed rules:

```javascript
// Test files configuration
{
  files: ['**/*.{test,spec}.{ts,tsx}', '**/__tests__/**/*.{ts,tsx}'],
  rules: {
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-unsafe-assignment': 'off',
    // ... other test-specific rule overrides
  },
}
```

## Best Practices

1. **Test Pure Functions**: Focus on functions without side effects
2. **Comprehensive Coverage**: Test normal cases, edge cases, and error conditions
3. **Descriptive Names**: Use clear, descriptive test and describe block names
4. **Factory Functions**: Use factory functions for creating consistent test data
5. **Isolated Tests**: Each test should be independent and not rely on others
6. **Mock External Dependencies**: Mock any external dependencies or APIs

## Future Enhancements

1. **Integration Tests**: Add tests for API routes and server actions
2. **Component Tests**: Add React component tests using React Testing Library
3. **E2E Tests**: Consider adding end-to-end tests for critical user flows
4. **Performance Tests**: Add performance benchmarks for critical functions
5. **Snapshot Tests**: Add snapshot tests for UI components

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed and paths are correct
2. **TypeScript Errors**: Check tsconfig.json includes test files
3. **Coverage Issues**: Verify coverage configuration in vitest.config.ts
4. **Environment Variables**: Check test setup file for required env vars

### Debug Commands

```bash
# Run specific test file
pnpm vitest src/__tests__/git-utils.test.ts

# Run tests with verbose output
pnpm vitest --reporter=verbose

# Run tests with debugging
pnpm vitest --inspect-brk
```

This testing setup provides a solid foundation for maintaining code quality and ensuring the reliability of pure functions throughout the project.

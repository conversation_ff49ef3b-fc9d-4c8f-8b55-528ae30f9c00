/**
 * Application configuration from environment variables
 */

// Repository paths
export const REPO_PATH = process.env.REPO_PATH ?? "";

// JIRA settings
export const JIRA_BASE_URL = process.env.JIRA_BASE_URL ?? "";
// JIRA_HOME is kept for backward compatibility but uses JIRA_BASE_URL
export const JIRA_HOME = process.env.JIRA_BASE_URL ?? "";
export const JIRA_API_TOKEN = process.env.JIRA_API_TOKEN ?? "";
export const JIRA_USERNAME = process.env.JIRA_USERNAME ?? "";
export const JIRA_PASSWORD = process.env.JIRA_PASSWORD ?? "";
export const JIRA_AUTHORIZATION = process.env.JIRA_AUTHORIZATION ?? "";
export const JIRA_JSESSIONID = process.env.JIRA_JSESSIONID ?? "";
export const JIRA_COOKIE = process.env.JIRA_COOKIE ?? "";
export const JIRA_SYNC_INTERVAL_MINUTES = process.env.JIRA_SYNC_INTERVAL_MINUTES
  ? Number.parseInt(process.env.JIRA_SYNC_INTERVAL_MINUTES, 10)
  : 5;
export const JIRA_JQL =
  process.env.JIRA_JQL ?? "project = JGCPS AND updated >= -7d";

// Application settings
export const NEXT_PUBLIC_APP_URL =
  process.env.NEXT_PUBLIC_APP_URL ?? "http://localhost:3000";

// Data storage paths
export const DATA_TASKS_DIR = process.env.DATA_TASKS_DIR ?? "data/tasks";
export const DATA_LOGS_DIR = process.env.DATA_LOGS_DIR ?? "data/logs";

// Aider settings
export const AIDER_ARCHITECT = process.env.AIDER_ARCHITECT === "true";
export const AIDER_MODEL = process.env.AIDER_MODEL ?? "";
export const AIDER_EDITOR_MODEL = process.env.AIDER_EDITOR_MODEL ?? "";
export const OPENAI_API_KEY = process.env.OPENAI_API_KEY ?? "";
export const OPENAI_BASE_URL = process.env.OPENAI_BASE_URL ?? "";

// Git branch patterns
export const GIT_BRANCH_PATTERN_BASE =
  process.env.GIT_BRANCH_PATTERN_BASE ?? "{VERSION}_dev";
export const GIT_BRANCH_PATTERN_ISSUE =
  process.env.GIT_BRANCH_PATTERN_ISSUE ?? "{VERSION}_dev_{ISSUE}";
export const GIT_BRANCH_PATTERN_TASK =
  process.env.GIT_BRANCH_PATTERN_TASK ?? "{VERSION}_dev_{ISSUE}_{COMMENT}";

import { Task } from "@/types/task";
import * as taskQueue from "@/lib/task-queue";
import AITasksClient from "@/components/ai-tasks-client";

// Server component that fetches initial data
export default function AITasksPage() {
  // Initialize task queue to ensure data is loaded
  taskQueue.initialize();

  // Fetch initial tasks on the server
  const initialTasks: Task[] = taskQueue.getAllTasks().map((task) => ({
    id: task.id,
    issueKey: task.issueKey,
    commentId: task.commentId,
    description: task.description,
    version: task.version,
    status: task.status,
    createdAt: task.createdAt,
    updatedAt: task.updatedAt,
    logs: task.logs,
    aiTool: task.aiTool,
    branch: task.branch,
  }));

  // Pass initial data to client component
  return <AITasksClient initialTasks={initialTasks} />;
}

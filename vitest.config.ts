/// <reference types="vitest" />
import { defineConfig } from "vite";
import path from "node:path";

export default defineConfig({
  test: {
    // Enable jsdom environment for DOM testing if needed
    environment: "jsdom",

    // Setup files to run before each test
    setupFiles: ["./src/__tests__/setup.ts"],

    // Include test files
    include: [
      "src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}",
      "src/**/__tests__/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}",
    ],

    // Exclude files
    exclude: ["node_modules", "dist", ".next", "coverage", "**/*.d.ts"],

    // Coverage configuration
    coverage: {
      provider: "v8",
      reporter: ["text", "json"],
      exclude: [
        "coverage/**",
        "dist/**",
        ".next/**",
        "node_modules/**",
        "src/**/*.d.ts",
        "src/**/*.config.*",
        "src/**/types/**",
        "**/*.test.*",
        "**/*.spec.*",
        "**/test/**",
        "**/__tests__/**",
      ],
    },

    // Global test configuration
    globals: true,

    // Reporter configuration
    reporters: ["verbose"],

    // Test timeout
    testTimeout: 10_000,
  },

  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "./src"),
    },
  },
});

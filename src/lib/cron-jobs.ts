import * as jiraSync from "./jira-sync";
import { JIRA_SYNC_INTERVAL_MINUTES } from "@/config";

// Module state
let syncInterval: NodeJS.Timeout | undefined;
let syncIntervalMinutes: number = JIRA_SYNC_INTERVAL_MINUTES; // Default from config

/**
 * Start the cron job to sync <PERSON>ra comments
 * @param intervalMinutes Optional interval in minutes (default: 5)
 */
export function startJiraSyncJob(intervalMinutes?: number): void {
  // Clear any existing interval
  stopJiraSyncJob();

  // Set the interval
  if (intervalMinutes && intervalMinutes > 0) {
    syncIntervalMinutes = intervalMinutes;
  }

  console.log(
    `Starting Jira sync job with interval of ${String(syncIntervalMinutes)} minutes`,
  );

  // Run immediately
  void syncJiraComments();

  // Set up interval
  const intervalMs = syncIntervalMinutes * 60 * 1000;
  syncInterval = setInterval(() => {
    void syncJiraComments();
  }, intervalMs);
}

/**
 * Stop the Jira sync cron job
 */
export function stopJiraSyncJob(): void {
  if (!syncInterval) {
    return;
  }

  clearInterval(syncInterval);
  syncInterval = undefined;
  console.log("Stopped Jira sync job");
}

/**
 * Sync Jira comments
 */
async function syncJiraComments(): Promise<void> {
  try {
    console.log("Running scheduled Jira sync job...");
    await jiraSync.syncComments();
    console.log("Jira sync job completed successfully");
  } catch (error) {
    console.error("Error in Jira sync job:", error);
  }
}

/**
 * Get the current sync interval in minutes
 */
export function getSyncIntervalMinutes(): number {
  return syncIntervalMinutes;
}

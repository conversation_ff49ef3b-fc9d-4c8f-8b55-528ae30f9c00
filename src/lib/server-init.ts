import * as cronJobs from "./cron-jobs";
import { JIRA_SYNC_INTERVAL_MINUTES } from "@/config";

/**
 * Initialize server-side components
 */
export function initializeServer(): void {
  console.log("Initializing server-side components...");

  // Start the Jira sync cron job with the sync interval from config
  cronJobs.startJiraSyncJob(JIRA_SYNC_INTERVAL_MINUTES);

  console.log("Server-side components initialized successfully");
}

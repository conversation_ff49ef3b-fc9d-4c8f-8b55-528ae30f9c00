import { NextResponse } from "next/server";
import * as cronJobs from "@/lib/cron-jobs";

export function POST() {
  try {
    const currentInterval = cronJobs.getSyncIntervalMinutes();

    // Restart the job with the current interval
    cronJobs.startJiraSyncJob(currentInterval);

    return NextResponse.json({
      success: true,
      message: "Jira sync job restarted successfully",
      interval: currentInterval,
    });
  } catch (error) {
    console.error("Error restarting Jira sync job:", error);
    return NextResponse.json(
      { error: "Failed to restart Jira sync job" },
      { status: 500 },
    );
  }
}

export function GET() {
  try {
    const currentInterval = cronJobs.getSyncIntervalMinutes();

    return NextResponse.json({
      success: true,
      interval: currentInterval,
    });
  } catch (error) {
    console.error("Error getting Jira sync job interval:", error);
    return NextResponse.json(
      { error: "Failed to get Jira sync job interval" },
      { status: 500 },
    );
  }
}

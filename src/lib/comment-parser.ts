import {
  <PERSON>ra<PERSON>om<PERSON>,
  Parsed<PERSON><PERSON>omment,
  AITask<PERSON>omment,
  JiraIssue,
} from "@/types/jira";

/**
 * AI Task Comment Format:
 * ```ai-task
 * tool: aider (optional, defaults to aider)
 * description: |
 *   Task description here
 *   Multiple lines supported
 * ```
 */

// Constants
const AI_TASK_MARKER = "```ai-task";
const CODE_BLOCK_END = "```";

/**
 * Parse a Jira comment to check if it contains an AI task
 */
export function parseComment(
  comment: JiraComment,
  issue: JiraIssue,
): ParsedAIComment {
  if (!comment.body.includes(AI_TASK_MARKER)) {
    return {
      isAITask: false,
      originalComment: comment,
    };
  }

  try {
    const taskBlock = extractTaskBlock(comment.body);
    if (!taskBlock) {
      return {
        isAITask: false,
        originalComment: comment,
      };
    }

    const taskInfo = parseTaskBlock(taskBlock, issue);

    return {
      isAITask: true,
      taskInfo,
      originalComment: comment,
    };
  } catch (error) {
    console.error("Error parsing AI task comment:", error);
    return {
      isAITask: false,
      originalComment: comment,
    };
  }
}

/**
 * Extract the AI task block from a comment body
 */
function extractTaskBlock(commentBody: string): string | undefined {
  const startIndex = commentBody.indexOf(AI_TASK_MARKER);
  if (startIndex === -1) return undefined;

  const endIndex = commentBody.indexOf(
    CODE_BLOCK_END,
    startIndex + AI_TASK_MARKER.length,
  );
  if (endIndex === -1) return undefined;

  return commentBody.slice(startIndex + AI_TASK_MARKER.length, endIndex).trim();
}

/**
 * Parse the task block to extract tool, description, etc.
 */
function parseTaskBlock(block: string, issue: JiraIssue): AITaskComment {
  const lines = block.split("\n");
  let aiTool: "aider" | "openhands" = "aider"; // default tool
  const description: string[] = [];
  let isDescription = false;

  // Get version from fixVersions, default to the first one
  const version = issue.fields.fixVersions[0]?.name ?? "3.11.1"; // fallback version

  for (const line of lines) {
    const trimmedLine = line.trim();

    if (trimmedLine.startsWith("tool:")) {
      aiTool = trimmedLine.slice("tool:".length).trim() as
        | "aider"
        | "openhands";
    } else if (trimmedLine.startsWith("description:")) {
      isDescription = true;
      if (trimmedLine.length > "description:".length) {
        description.push(trimmedLine.slice("description:".length).trim());
      }
    } else if (isDescription && trimmedLine) {
      description.push(trimmedLine);
    }
  }

  const branch = `${version}_dev_${issue.key}`;

  return {
    issueKey: issue.key,
    version,
    taskDescription: description.join("\n").trim(),
    aiTool,
    branch,
  };
}

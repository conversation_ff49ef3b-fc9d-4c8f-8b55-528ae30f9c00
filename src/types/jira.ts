export interface JiraIssue {
  expand: string;
  id: string;
  self: string;
  key: string;
  fields: <PERSON><PERSON><PERSON><PERSON><PERSON>;
}

export interface JiraFields {
  lastViewed: null;
  labels: string[];
  aggregatetimeoriginalestimate: number;
  issuelinks: Issuelink[];
  assignee: <PERSON><PERSON><PERSON><PERSON>;
  components: unknown[];
  subtasks: unknown[];
  reporter: <PERSON><PERSON><PERSON><PERSON>;
  progress: Progress;
  votes: Votes;
  worklog: WorklogClass;
  issuetype: Issuetype;
  project: Project;
  resolutiondate: string;
  watches: Watches;
  updated: string;
  timeoriginalestimate: number;
  description: string;
  timetracking: Timetracking;
  summary: string;
  environment: null;
  duedate: null;
  comment: WorklogClass;
  fixVersions: Version[];
  priority: Priority;
  timeestimate: number;
  versions: Version[];
  status: Status;
  aggregatetimeestimate: number;
  creator: <PERSON><PERSON><PERSON><PERSON>;
  aggregateprogress: Progress;
  timespent: number;
  aggregatetimespent: number;
  workratio: number;
  created: string;
  attachment: Attachment[];
}

export interface Progress {
  progress: number;
  total: number;
  percent: number;
}

export interface JiraUser {
  self: string;
  name: string;
  key: string;
  emailAddress: string;
  avatarUrls: AvatarUrls;
  displayName: string;
  active: boolean;
  timeZone: TimeZone;
}

export interface AvatarUrls {
  "48x48": string;
  "24x24": string;
  "16x16": string;
  "32x32": string;
}
export enum TimeZone {
  AsiaShanghai = "Asia/Shanghai",
}

export interface Attachment {
  self: string;
  id: string;
  filename: string;
  author: JiraUser;
  created: string;
  size: number;
  mimeType: string;
  content: string;
  thumbnail: string;
}

export interface WorklogClass {
  comments?: JiraComment[];
  maxResults: number;
  total: number;
  startAt: number;
  worklogs?: Worklog[];
}

export interface JiraComment {
  self: string;
  id: string;
  author: JiraUser;
  body: string;
  updateAuthor: JiraUser;
  created: string;
  updated: string;
}

export interface Worklog {
  self: string;
  author: JiraUser;
  updateAuthor: JiraUser;
  comment: string;
  created: string;
  updated: string;
  started: string;
  timeSpent: string;
  timeSpentSeconds: number;
  id: string;
  issueId: string;
}

export interface Version {
  self: string;
  id: string;
  description: string;
  name: string;
  archived: boolean;
  released: boolean;
}

export interface Issuelink {
  id: string;
  self: string;
  type: Type;
  inwardIssue: InwardIssue;
}

export interface InwardIssue {
  id: string;
  key: string;
  self: string;
  fields: InwardIssueFields;
}

export interface InwardIssueFields {
  summary: string;
  status: Status;
  priority: Priority;
  issuetype: Issuetype;
}

export interface Issuetype {
  self: string;
  id: string;
  description: string;
  iconUrl: string;
  name: string;
  subtask: boolean;
  avatarId: number;
}

export interface Priority {
  self: string;
  iconUrl?: string;
  name: string;
  id: string;
  description?: string;
}

export interface Status {
  self: string;
  description: string;
  iconUrl: string;
  name: string;
  id: string;
  statusCategory: StatusCategory;
}

export interface StatusCategory {
  self: string;
  id: number;
  key: string;
  colorName: string;
  name: string;
}

export interface Type {
  id: string;
  name: string;
  inward: string;
  outward: string;
  self: string;
}

export interface Project {
  self: string;
  id: string;
  key: string;
  name: string;
  projectTypeKey: string;
  avatarUrls: AvatarUrls;
  projectCategory: Priority;
}

export interface Timetracking {
  originalEstimate: string;
  remainingEstimate: string;
  timeSpent: string;
  originalEstimateSeconds: number;
  remainingEstimateSeconds: number;
  timeSpentSeconds: number;
}

export interface Votes {
  self: string;
  votes: number;
  hasVoted: boolean;
}

export interface Watches {
  self: string;
  watchCount: number;
  isWatching: boolean;
}

export interface JiraSearchRequest {
  jql: string;
  startAt?: number;
  maxResults?: number;
  fields?: string[];
}

export interface JiraSearchResponse {
  expand: string;
  startAt: number;
  maxResults: number;
  total: number;
  issues: JiraIssue[];
}

export interface JiraError {
  errorMessages: string[];
  errors: Record<string, string>;
}

export interface AIComment {
  id: string;
  commentId: string;
  issueKey: string;
  comment: string;
  content: string;
  created: string;
  author: string;
  type: "task" | "response";
  issueSummary: string;
  issueStatus: string;
  taskInfo?: {
    version: string;
    description: string;
    aiTool: "aider" | "openhands";
    branch?: string;
  };
}

export interface AITaskComment {
  issueKey: string;
  version: string;
  taskDescription: string;
  aiTool: "aider" | "openhands";
  branch?: string;
}

export interface ParsedAIComment {
  isAITask: boolean;
  taskInfo?: AITaskComment;
  originalComment: JiraComment;
}

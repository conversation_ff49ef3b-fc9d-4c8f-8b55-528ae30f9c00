import spawn from "nano-spawn";
import {
  GIT_BRANCH_PATTERN_BASE,
  GIT_BRANCH_PATTERN_ISSUE,
  GIT_BRANCH_PATTERN_TASK,
} from "@/config";

/**
 * Log a message using the provided callback
 */
function log(message: string, logCallback?: (message: string) => void): void {
  if (logCallback) {
    logCallback(message);
  }
}

/**
 * Execute a git command
 */
async function execute(
  command: string,
  arguments_: string[],
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<string> {
  log(`Executing: ${command} ${arguments_.join(" ")}`, logCallback);

  try {
    const { stdout, stderr } = await spawn(command, arguments_, {
      cwd: repoPath,
    });

    if (stderr) {
      log(`stderr: ${stderr}`, logCallback);
    }

    log(`stdout: ${stdout}`, logCallback);
    return stdout;
  } catch (error) {
    const error_ = error as Error;
    log(`Error: ${error_.message}`, logCallback);
    throw error;
  }
}

/**
 * Checkout a git branch
 */
export async function checkoutBranch(
  branchName: string,
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<void> {
  try {
    await execute("git", ["checkout", branchName], repoPath, logCallback);
    log(`Successfully checked out branch: ${branchName}`, logCallback);
  } catch {
    log(`Branch ${branchName} does not exist. Will create it.`, logCallback);
    await createBranch(branchName, repoPath, logCallback);
  }
}

/**
 * Create a new git branch
 */
export async function createBranch(
  branchName: string,
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<void> {
  await execute("git", ["checkout", "-b", branchName], repoPath, logCallback);
  log(
    `Successfully created and checked out branch: ${branchName}`,
    logCallback,
  );
}

/**
 * Check if a repository has a remote
 */
export async function hasRemote(
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<boolean> {
  try {
    const output = await execute("git", ["remote"], repoPath, logCallback);
    return output.trim() !== "";
  } catch {
    log("Error checking for remote, assuming no remote exists", logCallback);
    return false;
  }
}

/**
 * Pull with rebase
 */
export async function pullRebase(
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<void> {
  // Check if the repository has a remote before attempting to pull
  if (await hasRemote(repoPath, logCallback)) {
    try {
      await execute("git", ["pull", "-r"], repoPath, logCallback);
      log("Successfully pulled with rebase", logCallback);
    } catch (error) {
      const error_ = error as Error;
      log(
        `Error during pull: ${error_.message}. Continuing without pull.`,
        logCallback,
      );
    }
  } else {
    log("Repository has no remote, skipping git pull", logCallback);
  }
}

/**
 * Get the current branch
 */
export async function getCurrentBranch(
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<string> {
  const output = await execute(
    "git",
    ["branch", "--show-current"],
    repoPath,
    logCallback,
  );
  return output.trim();
}

/**
 * Check if a branch exists
 */
export async function branchExists(
  branchName: string,
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<boolean> {
  try {
    const output = await execute(
      "git",
      ["branch", "--list", branchName],
      repoPath,
      logCallback,
    );
    return output.trim() !== "";
  } catch {
    return false;
  }
}

/**
 * Check if there are any changes to commit
 */
export async function hasChangesToCommit(
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<boolean> {
  try {
    const output = await execute(
      "git",
      ["status", "--porcelain"],
      repoPath,
      logCallback,
    );
    return output.trim() !== "";
  } catch (error) {
    const error_ = error as Error;
    log(`Error checking git status: ${error_.message}`, logCallback);
    return false;
  }
}

/**
 * Add all changes to git staging area
 */
export async function addAllChanges(
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<void> {
  await execute("git", ["add", "."], repoPath, logCallback);
  log("Successfully added all changes to staging area", logCallback);
}

/**
 * Commit changes with a message
 */
export async function commitChanges(
  message: string,
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<void> {
  await execute("git", ["commit", "-m", message], repoPath, logCallback);
  log(`Successfully committed changes with message: ${message}`, logCallback);
}

/**
 * Push the current branch to remote
 */
export async function pushBranch(
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<void> {
  // Check if the repository has a remote before attempting to push
  if (await hasRemote(repoPath, logCallback)) {
    try {
      // Get current branch name
      const currentBranch = await getCurrentBranch(repoPath, logCallback);

      // Push with set-upstream to handle new branches
      await execute(
        "git",
        ["push", "--set-upstream", "origin", currentBranch],
        repoPath,
        logCallback,
      );
      log(`Successfully pushed branch: ${currentBranch}`, logCallback);
    } catch (error) {
      const error_ = error as Error;
      log(`Error during push: ${error_.message}`, logCallback);
      throw error;
    }
  } else {
    log("Repository has no remote, skipping git push", logCallback);
  }
}

/**
 * Commit and push changes after AI task completion
 */
export async function commitAndPushChanges(
  issueKey: string,
  commentId: string,
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<void> {
  // Check if there are any changes to commit
  if (!(await hasChangesToCommit(repoPath, logCallback))) {
    log("No changes to commit", logCallback);
    return;
  }

  // Add all changes
  await addAllChanges(repoPath, logCallback);

  // Create commit message
  const commitMessage = `AI task completion for ${issueKey} (comment: ${commentId})`;

  // Commit changes
  await commitChanges(commitMessage, repoPath, logCallback);

  // Push changes
  await pushBranch(repoPath, logCallback);
}

/**
 * Setup git branches for an AI task
 */
export async function setupForAITask(
  fixVersion: string,
  issueKey: string,
  commentId: string,
  repoPath: string,
  logCallback?: (message: string) => void,
): Promise<string> {
  // Step 1: Checkout the base branch using the configured pattern
  const baseBranch = GIT_BRANCH_PATTERN_BASE.replace("{VERSION}", fixVersion);
  await checkoutBranch(baseBranch, repoPath, logCallback);
  await pullRebase(repoPath, logCallback);

  // Step 2: Checkout to issue branch using the configured pattern
  const issueBranch = GIT_BRANCH_PATTERN_ISSUE.replace(
    "{VERSION}",
    fixVersion,
  ).replace("{ISSUE}", issueKey);

  if (await branchExists(issueBranch, repoPath, logCallback)) {
    await checkoutBranch(issueBranch, repoPath, logCallback);
    await pullRebase(repoPath, logCallback);
  } else {
    await createBranch(issueBranch, repoPath, logCallback);
  }

  // Step 3: Checkout to a new task branch using the configured pattern
  const taskBranch = GIT_BRANCH_PATTERN_TASK.replace("{VERSION}", fixVersion)
    .replace("{ISSUE}", issueKey)
    .replace("{COMMENT}", commentId);

  await createBranch(taskBranch, repoPath, logCallback);

  return taskBranch;
}

/**
 * Pure utility functions for string manipulation
 * These functions don't have side effects and can be easily tested
 */

/**
 * Sanitize a string for safe use in command line arguments
 * Removes or escapes special shell characters
 */
export function sanitizeCommandInput(input: string): string {
  // Remove YAML pipe symbol and newline that follows it
  let sanitized = input.replaceAll("|\n", "");

  // Remove other potentially problematic shell characters
  // or escape them as needed
  sanitized = sanitized.replaceAll(/[&;|<>$`"'\\]/g, "");

  return sanitized.trim();
}

/**
 * Generate a timestamp string for container names
 */
export function generateTimestamp(): string {
  return new Date().toISOString().replaceAll(/\D/g, "").slice(0, 14);
}

/**
 * Extract content between markers
 */
export function extractContentBetweenMarkers(
  text: string,
  startMarker: string,
  endMarker: string,
): string | undefined {
  const startIndex = text.indexOf(startMarker);
  if (startIndex === -1) return undefined;

  const endIndex = text.indexOf(endMarker, startIndex + startMarker.length);
  if (endIndex === -1) return undefined;

  return text.slice(startIndex + startMarker.length, endIndex).trim();
}

/**
 * Parse key-value pairs from text lines
 */
export function parseKeyValueLines(
  lines: string[],
  keyValueSeparator = ":",
): Record<string, string> {
  const result: Record<string, string> = {};

  for (const line of lines) {
    const trimmedLine = line.trim();
    const separatorIndex = trimmedLine.indexOf(keyValueSeparator);

    if (separatorIndex > 0) {
      const key = trimmedLine.slice(0, separatorIndex).trim();
      const value = trimmedLine.slice(separatorIndex + 1).trim();
      result[key] = value;
    }
  }

  return result;
}

/**
 * Join non-empty strings with a separator
 */
export function joinNonEmpty(strings: string[], separator = " "): string {
  return strings.filter((s) => s.trim().length > 0).join(separator);
}

/**
 * Truncate text to a maximum length with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }

  return `${text.slice(0, maxLength - 3)}...`;
}

/**
 * Convert camelCase to kebab-case
 */
export function camelToKebab(string_: string): string {
  return string_
    .replaceAll(/([a-z0-9])([A-Z])/g, "$1-$2")
    .replaceAll(/([A-Z])([A-Z][a-z])/g, "$1-$2")
    .toLowerCase();
}

/**
 * Convert kebab-case to camelCase
 */
export function kebabToCamel(string_: string): string {
  return string_.replaceAll(/-([a-z])/g, (_, letter: string) =>
    letter.toUpperCase(),
  );
}

/**
 * Escape special regex characters in a string
 */
export function escapeRegex(string_: string): string {
  return string_.replaceAll(/[.*+?^${}()|[\]\\]/g, String.raw`\$&`);
}

/**
 * Check if a string is a valid URL
 */
export function isValidUrl(string_: string): boolean {
  try {
    new URL(string_);
    return true;
  } catch {
    return false;
  }
}

# Repository paths
REPO_PATH=/path/to/gf-trader-app

# JIRA settings
JIRA_BASE_URL=https://your-jira-instance.atlassian.net
JIRA_API_TOKEN=your-jira-api-token

# JIRA authentication (choose one method)
# Method 1: Basic auth with username/password
JIRA_USERNAME=your-username
JIRA_PASSWORD=your-password

# Method 2: Basic auth with authorization token
# JIRA_AUTHORIZATION=base64-encoded-token

# Method 3: Cookie-based auth
# JIRA_JSESSIONID=your-jsessionid-value
# JIRA_COOKIE=your-custom-cookie

# JIRA sync settings
JIRA_SYNC_INTERVAL_MINUTES=5
JIRA_JQL=project = JGCPS AND updated >= -7d

# Application settings
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Data storage paths
DATA_TASKS_DIR=data/tasks
DATA_LOGS_DIR=data/logs

# Git branch patterns
GIT_BRANCH_PATTERN_BASE={VERSION}_dev
GIT_BRANCH_PATTERN_ISSUE={VERSION}_dev_{ISSUE}
GIT_BRANCH_PATTERN_TASK={VERSION}_dev_{ISSUE}_{COMMENT}

# Aider settings
# AIDER_ARCHITECT=true
# AIDER_MODEL=openai/internal-qwq-32b
# AIDER_EDITOR_MODEL=openai/internal-qwen2.5-72b-turbo
# OPENAI_API_KEY=openai=9d020b83-5c78-402f-9c7f-30815f8de1
# OPENAI_BASE_URL=http://llm.smart-zone-dev.gf.com.cn/api/oai/v1

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",

  // Add webpack configuration to handle node: protocol imports
  webpack: (config, { isServer }) => {
    // Add fallbacks for Node.js built-in modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        child_process: false,
        fs: false,
        path: false,
        os: false,
        crypto: false,
      };
    }

    return config;
  },
};

export default nextConfig;
import { Task, TaskLog, TaskStatus } from "@/types/task";
import fs from "node:fs";
import path from "node:path";
import * as aiTaskExecutor from "./ai-task-executor";
import { DATA_TASKS_DIR, DATA_LOGS_DIR } from "@/config";

// Module state
const tasks = new Map<string, Task>();
const tasksDirectory = path.join(process.cwd(), DATA_TASKS_DIR);
const logsDirectory = path.join(process.cwd(), DATA_LOGS_DIR);
let isProcessing = false;
let currentTask: string | null = null;

/**
 * Initialize the task queue
 */
export function initialize(): void {
  ensureDirectoriesExist();
  loadTasks();
}

/**
 * Ensure the task and log directories exist
 */
function ensureDirectoriesExist(): void {
  if (!fs.existsSync(tasksDirectory)) {
    fs.mkdirSync(tasksDirectory, { recursive: true });
  }

  if (!fs.existsSync(logsDirectory)) {
    fs.mkdirSync(logsDirectory, { recursive: true });
  }
}

/**
 * Load tasks from the file system
 */
function loadTasks(): void {
  try {
    const files = fs.readdirSync(tasksDirectory);

    for (const file of files) {
      if (file.endsWith(".json")) {
        const taskData = fs.readFileSync(
          path.join(tasksDirectory, file),
          "utf8",
        );
        const task = JSON.parse(taskData) as Task;
        tasks.set(task.id, task);
      }
    }
  } catch (error) {
    console.error("Error loading tasks:", error);
  }
}

/**
 * Save a task to the file system
 */
function saveTask(task: Task): void {
  const filePath = path.join(tasksDirectory, `${task.id}.json`);
  fs.writeFileSync(filePath, JSON.stringify(task, null, 2));
  tasks.set(task.id, task);
}

/**
 * Save logs to the file system
 */
function saveLogs(taskId: string, logs: TaskLog[]): void {
  const filePath = path.join(logsDirectory, `${taskId}.json`);
  fs.writeFileSync(filePath, JSON.stringify(logs, null, 2));
}

/**
 * Add a new task to the queue
 */
export function addTask(
  task: Omit<Task, "status" | "createdAt" | "updatedAt" | "logs">,
): Task {
  const now = new Date().toISOString();
  const newTask: Task = {
    ...task,
    status: "pending",
    createdAt: now,
    updatedAt: now,
    logs: [],
  };

  saveTask(newTask);
  void processQueue();

  return newTask;
}

/**
 * Get a task by ID
 */
export function getTask(taskId: string): Task | undefined {
  return tasks.get(taskId);
}

/**
 * Get all tasks
 */
export function getAllTasks(): Task[] {
  return Array.from(tasks.values());
}

/**
 * Get logs for a task
 */
export function getTaskLogs(taskId: string): TaskLog[] {
  try {
    const filePath = path.join(logsDirectory, `${taskId}.json`);
    if (fs.existsSync(filePath)) {
      const logsData = fs.readFileSync(filePath, "utf8");
      return JSON.parse(logsData) as TaskLog[];
    }
  } catch (error) {
    console.error(`Error reading logs for task ${taskId}:`, error);
  }

  return [];
}

/**
 * Update a task's status
 */
export function updateTaskStatus(taskId: string, status: TaskStatus): void {
  const task = tasks.get(taskId);
  if (task) {
    task.status = status;
    task.updatedAt = new Date().toISOString();
    saveTask(task);
  }
}

/**
 * Add a log entry to a task
 */
export function addTaskLog(
  taskId: string,
  log: Omit<TaskLog, "timestamp">,
): void {
  const task = tasks.get(taskId);
  if (task) {
    const newLog: TaskLog = {
      ...log,
      timestamp: new Date().toISOString(),
    };

    task.logs.push(newLog);
    saveTask(task);

    // Also save logs to a separate file for better performance when reading logs
    const logs = getTaskLogs(taskId);
    logs.push(newLog);
    saveLogs(taskId, logs);
  }
}

/**
 * Update a task
 */
export function updateTask(
  taskId: string,
  updates: Partial<Omit<Task, "id" | "createdAt">>,
): Task | undefined {
  const task = tasks.get(taskId);
  if (task) {
    // Only allow updating fields that are not id or createdAt
    const { status, description, version, branch, logs, aiTool } = updates;

    if (status) task.status = status;
    if (description) task.description = description;
    if (version) task.version = version;
    if (branch !== undefined) task.branch = branch;
    if (logs) task.logs = logs;
    if (aiTool) task.aiTool = aiTool;

    // Always update updatedAt when task is modified
    task.updatedAt = new Date().toISOString();

    saveTask(task);
    return task;
  }
  return undefined;
}

/**
 * Update a task's branch
 */
export function updateTaskBranch(taskId: string, branch: string): void {
  updateTask(taskId, { branch });
}

/**
 * Process the task queue
 */
async function processQueue(): Promise<void> {
  if (isProcessing) {
    return;
  }

  isProcessing = true;

  try {
    // Find the next pending task
    const pendingTasks = Array.from(tasks.values())
      .filter((task) => task.status === "pending")
      .sort(
        (a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      );

    if (pendingTasks.length === 0) {
      isProcessing = false;
      return;
    }

    const nextTask = pendingTasks[0];
    currentTask = nextTask.id;

    // Update task status to running
    updateTaskStatus(nextTask.id, "running");

    // Execute the task
    const logCallback = (log: Omit<TaskLog, "timestamp">) => {
      addTaskLog(nextTask.id, log);
    };
    const result = await aiTaskExecutor.execute(nextTask, logCallback);

    // Update task status based on result
    if (result.success) {
      updateTaskStatus(nextTask.id, "completed");
      if (result.branch) {
        updateTaskBranch(nextTask.id, result.branch);
      }
    } else {
      updateTaskStatus(nextTask.id, "failed");
      addTaskLog(nextTask.id, {
        type: "error",
        message: result.error ?? "Task execution failed",
      });
    }
  } catch (error) {
    const error_ = error as Error;
    if (currentTask) {
      updateTaskStatus(currentTask, "failed");
      addTaskLog(currentTask, {
        type: "error",
        message: `Unexpected error: ${error_.message}`,
      });
    }
  } finally {
    currentTask = null;
    isProcessing = false;

    // Process the next task in the queue
    setTimeout(() => {
      void processQueue();
    }, 1000);
  }
}

// Initialize the task queue
initialize();

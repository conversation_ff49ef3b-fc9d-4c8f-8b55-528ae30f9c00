{
  "$schema": "https://unpkg.com/knip@latest/schema.json",
  "entry": [
    "src/app/**/*.{ts,tsx}",
  ],
  "project": [
    "src/**/*.{ts,tsx}"
  ],
  "ignoreDependencies": [
    "autoprefixer",
    "postcss",
    "tailwindcss"
  ],
  "ignoreBinaries": [],
  "ignore": [
    "**/__tests__/**",
    "**/*.test.{ts,tsx}",
    "**/*.spec.{ts,tsx}"
  ],
  "rules": {
    "classMembers": "error",
    "enumMembers": "error",
    "exports": "error",
    "files": "error",
    "nsExports": "error",
    "types": "error",
    "dependencies": "error",
    "devDependencies": "error",
    "unlisted": "error"
  }
}

# Manual AI Task Creation Feature

## Overview

This document describes the new manual AI task creation feature that allows users to create AI tasks directly through a web form, without needing to sync from <PERSON><PERSON>.

## Features Added

### 1. New Create Task Page (`/ai-tasks/create`)

A dedicated page for manually creating AI tasks with the following features:

- **Form Fields:**

  - Issue Key (required) - <PERSON>ra issue key or any identifier
  - Comment ID (required) - Unique identifier for the task
  - Version (required) - Version or branch information
  - Task Description (required) - Detailed description of what the AI should do
  - AI Tool (optional) - Choice between "aider" and "openhands" (defaults to "aider")

- **Form Validation:**

  - Client-side validation for all required fields
  - User-friendly error messages
  - Prevents submission with missing data

- **User Experience:**
  - Loading states during form submission
  - Success redirect to AI tasks list
  - Error handling with clear feedback
  - Cancel button to return to tasks list

### 2. Navigation Updates

- **Home Page:** Added "Create AI Task" button with indigo styling
- **AI Tasks Page:** Added "Create New Task" button in the header next to the refresh button

### 3. API Integration

Uses the existing `POST /api/ai-tasks` endpoint with the following payload:

```json
{
  "issueKey": "string",
  "commentId": "string",
  "version": "string",
  "description": "string",
  "aiTool": "aider" | "openhands"
}
```

## File Structure

```
src/
├── app/
│   ├── ai-tasks/
│   │   ├── create/
│   │   │   └── page.tsx          # New manual task creation page
│   │   └── page.tsx              # Updated to show create button
│   └── page.tsx                  # Updated home page with create link
└── components/
    └── ai-tasks-client.tsx       # Updated with create button
```

## Usage Instructions

### Creating a Manual Task

1. Navigate to the home page (`/`)
2. Click "Create AI Task" button, or
3. Go to AI Tasks page (`/ai-tasks`) and click "Create New Task"
4. Fill in the required form fields:
   - **Issue Key:** Enter a Jira issue key (e.g., "JGCPS-123") or any identifier
   - **Comment ID:** Enter a unique identifier for this task
   - **Version:** Enter version or branch information
   - **Description:** Provide detailed instructions for the AI
   - **AI Tool:** Select between "aider" or "openhands"
5. Click "Create AI Task" to submit
6. You'll be redirected to the AI tasks list where you can see your new task

### Form Validation

The form validates that all required fields are filled before submission:

- Empty fields will show an error message
- The form cannot be submitted until all required fields have values
- Loading state prevents multiple submissions

## Technical Implementation

### Components

- **CreateAITaskPage:** Main form component with state management
- **Form Handling:** Uses React hooks for form state and validation
- **API Integration:** Calls existing task creation endpoint
- **Navigation:** Uses Next.js router for redirects

### Styling

- Consistent with existing design using Tailwind CSS
- Responsive layout that works on mobile and desktop
- Loading states with spinner animations
- Error states with red styling
- Success states with proper redirects

### Error Handling

- Network errors are caught and displayed to the user
- API errors are shown with specific error messages
- Form validation prevents invalid submissions
- Loading states prevent duplicate submissions

## Testing

To test the functionality:

1. Start the development server: `npm run dev`
2. Navigate to `http://localhost:3000`
3. Click "Create AI Task"
4. Fill out the form with test data
5. Submit and verify the task appears in the AI tasks list
6. Check that the task has the correct status ("pending")
7. Verify the task can be viewed in the tasks overview

## Integration with Existing System

The manual task creation integrates seamlessly with the existing system:

- Uses the same task queue and storage system
- Tasks created manually appear alongside Jira-synced tasks
- Same task execution pipeline (aider/openhands)
- Same logging and status tracking
- Same task management interface

## Future Enhancements

Potential improvements for the manual task creation feature:

1. **Form Presets:** Save common task templates
2. **Bulk Creation:** Create multiple tasks at once
3. **File Upload:** Allow uploading task descriptions from files
4. **Task Cloning:** Duplicate existing tasks with modifications
5. **Advanced Validation:** More sophisticated field validation
6. **Auto-completion:** Suggest values based on previous tasks

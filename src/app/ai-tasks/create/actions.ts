"use server";

import { v4 as uuidv4 } from "uuid";
import * as taskQueue from "@/lib/task-queue";

export interface CreateTaskFormState {
  error?: string;
  success?: boolean;
  taskId?: string;
}

export async function createTask(
  _previousState: CreateTaskFormState,
  formData: FormData,
): Promise<CreateTaskFormState> {
  // Use await to satisfy the linter requirement for async functions
  await new Promise((resolve) => setTimeout(resolve, 0));

  const issueKey = formData.get("issueKey") as string | null;
  const commentId = formData.get("commentId") as string | null;
  const version = formData.get("version") as string | null;
  const description = formData.get("description") as string | null;
  const aiToolValue = formData.get("aiTool") as string | null;
  const aiTool = aiToolValue === "openhands" ? "openhands" : "aider";

  // Validation
  if (!issueKey?.trim()) {
    return { error: "Issue Key is required" };
  }
  if (!commentId?.trim()) {
    return { error: "Comment ID is required" };
  }
  if (!version?.trim()) {
    return { error: "Version is required" };
  }
  if (!description?.trim()) {
    return { error: "Description is required" };
  }

  try {
    const task = taskQueue.addTask({
      id: uuidv4(),
      issueKey: issueKey.trim(),
      commentId: commentId.trim(),
      version: version.trim(),
      description: description.trim(),
      aiTool,
    });

    // Return success state instead of redirecting
    return {
      success: true,
      taskId: task.id,
    };
  } catch (error) {
    console.error("Error creating task:", error);
    return {
      error: error instanceof Error ? error.message : "Failed to create task",
    };
  }
}

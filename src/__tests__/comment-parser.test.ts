import { describe, it, expect } from "vitest";
import { parseComment } from "@/lib/comment-parser";
import type { <PERSON>raComment, JiraIssue } from "@/types/jira";

// Test data factories
const createMockJiraIssue = (
  overrides: Partial<JiraIssue> = {},
): JiraIssue => ({
  expand: "",
  id: "test-id",
  self: "test-self",
  key: "TEST-123",
  fields: {
    fixVersions: [
      {
        self: "",
        id: "1",
        description: "",
        name: "3.11.1",
        archived: false,
        released: false,
      },
    ],
    lastViewed: null,
    labels: [],
    aggregatetimeoriginalestimate: 0,
    issuelinks: [],
    assignee: {} as any,
    components: [],
    subtasks: [],
    reporter: {} as any,
    progress: { progress: 0, total: 0, percent: 0 },
    votes: { self: "", votes: 0, hasVoted: false },
    worklog: { maxResults: 0, total: 0, startAt: 0 },
    issuetype: {} as any,
    project: {} as any,
    resolutiondate: "",
    watches: { self: "", watchCount: 0, isWatching: false },
    updated: "",
    timeoriginalestimate: 0,
    description: "",
    timetracking: {} as any,
    summary: "",
    environment: null,
    duedate: null,
    comment: { maxResults: 0, total: 0, startAt: 0 },
    priority: {} as any,
    timeestimate: 0,
    versions: [],
    status: {} as any,
    aggregatetimeestimate: 0,
    creator: {} as any,
    aggregateprogress: { progress: 0, total: 0, percent: 0 },
    timespent: 0,
    aggregatetimespent: 0,
    workratio: 0,
    created: "",
    attachment: [],
  },
  ...overrides,
});

const createMockJiraComment = (
  body: string,
  overrides: Partial<JiraComment> = {},
): JiraComment => ({
  self: "test-self",
  id: "comment-123",
  author: {} as any,
  body,
  updateAuthor: {} as any,
  created: "2023-01-01T00:00:00.000Z",
  updated: "2023-01-01T00:00:00.000Z",
  ...overrides,
});

describe("parseComment", () => {
  describe("when comment does not contain AI task marker", () => {
    it("should return isAITask false for regular comment", () => {
      const comment = createMockJiraComment("This is a regular comment");
      const issue = createMockJiraIssue();

      const result = parseComment(comment, issue);

      expect(result.isAITask).toBe(false);
      expect(result.originalComment).toBe(comment);
      expect(result.taskInfo).toBeUndefined();
    });

    it("should return isAITask false for empty comment", () => {
      const comment = createMockJiraComment("");
      const issue = createMockJiraIssue();

      const result = parseComment(comment, issue);

      expect(result.isAITask).toBe(false);
      expect(result.originalComment).toBe(comment);
    });
  });

  describe("when comment contains AI task marker", () => {
    it("should parse basic AI task with default tool", () => {
      const commentBody = `
Some text before
\`\`\`ai-task
description: Fix the login bug
\`\`\`
Some text after
      `.trim();

      const comment = createMockJiraComment(commentBody);
      const issue = createMockJiraIssue();

      const result = parseComment(comment, issue);

      expect(result.isAITask).toBe(true);
      expect(result.taskInfo).toEqual({
        issueKey: "TEST-123",
        version: "3.11.1",
        taskDescription: "Fix the login bug",
        aiTool: "aider",
        branch: "3.11.1_dev_TEST-123",
      });
    });

    it("should parse AI task with specified tool", () => {
      const commentBody = `
\`\`\`ai-task
tool: openhands
description: Implement new feature
\`\`\`
      `.trim();

      const comment = createMockJiraComment(commentBody);
      const issue = createMockJiraIssue();

      const result = parseComment(comment, issue);

      expect(result.isAITask).toBe(true);
      expect(result.taskInfo?.aiTool).toBe("openhands");
      expect(result.taskInfo?.taskDescription).toBe("Implement new feature");
    });

    it("should parse multiline description", () => {
      const commentBody = `
\`\`\`ai-task
description: |
  This is a multiline description
  with multiple lines
  and more details
\`\`\`
      `.trim();

      const comment = createMockJiraComment(commentBody);
      const issue = createMockJiraIssue();

      const result = parseComment(comment, issue);

      expect(result.isAITask).toBe(true);
      expect(result.taskInfo?.taskDescription).toBe(
        "|\nThis is a multiline description\nwith multiple lines\nand more details",
      );
    });

    it("should handle description on same line as key", () => {
      const commentBody = `
\`\`\`ai-task
description: Single line description
\`\`\`
      `.trim();

      const comment = createMockJiraComment(commentBody);
      const issue = createMockJiraIssue();

      const result = parseComment(comment, issue);

      expect(result.isAITask).toBe(true);
      expect(result.taskInfo?.taskDescription).toBe("Single line description");
    });

    it("should use fallback version when fixVersions is empty", () => {
      const commentBody = `
\`\`\`ai-task
description: Test task
\`\`\`
      `.trim();

      const comment = createMockJiraComment(commentBody);
      const issue = createMockJiraIssue({
        fields: {
          ...createMockJiraIssue().fields,
          fixVersions: [],
        },
      });

      const result = parseComment(comment, issue);

      expect(result.isAITask).toBe(true);
      expect(result.taskInfo?.version).toBe("3.11.1"); // fallback version
      expect(result.taskInfo?.branch).toBe("3.11.1_dev_TEST-123");
    });

    it("should handle malformed AI task block gracefully", () => {
      const commentBody = `
\`\`\`ai-task
invalid format without proper structure
      `.trim(); // Note: missing closing ```

      const comment = createMockJiraComment(commentBody);
      const issue = createMockJiraIssue();

      const result = parseComment(comment, issue);

      expect(result.isAITask).toBe(false);
      expect(result.originalComment).toBe(comment);
    });

    it("should handle empty AI task block", () => {
      const commentBody = `
\`\`\`ai-task
\`\`\`
      `.trim();

      const comment = createMockJiraComment(commentBody);
      const issue = createMockJiraIssue();

      const result = parseComment(comment, issue);

      // Current implementation treats empty task blocks as invalid
      expect(result.isAITask).toBe(false);
      expect(result.originalComment).toBe(comment);
    });
  });

  describe("edge cases", () => {
    it("should handle multiple AI task blocks (uses first one)", () => {
      const commentBody = `
\`\`\`ai-task
description: First task
\`\`\`

\`\`\`ai-task
description: Second task
\`\`\`
      `.trim();

      const comment = createMockJiraComment(commentBody);
      const issue = createMockJiraIssue();

      const result = parseComment(comment, issue);

      expect(result.isAITask).toBe(true);
      expect(result.taskInfo?.taskDescription).toBe("First task");
    });

    it("should handle AI task marker in middle of other code blocks", () => {
      const commentBody = `
\`\`\`javascript
console.log('not an ai-task')
\`\`\`

\`\`\`ai-task
description: Real AI task
\`\`\`
      `.trim();

      const comment = createMockJiraComment(commentBody);
      const issue = createMockJiraIssue();

      const result = parseComment(comment, issue);

      expect(result.isAITask).toBe(true);
      expect(result.taskInfo?.taskDescription).toBe("Real AI task");
    });
  });
});

import { describe, it, expect, vi, beforeEach } from "vitest";
import * as gitOperations from "@/lib/git-operations";

// Mock nano-spawn
vi.mock("nano-spawn", () => ({
  default: vi.fn(),
}));

// Mock config
vi.mock("@/config", () => ({
  GIT_BRANCH_PATTERN_BASE: "{VERSION}_dev",
  GIT_BRANCH_PATTERN_ISSUE: "{VERSION}_dev_{ISSUE}",
  GIT_BRANCH_PATTERN_TASK: "{VERSION}_dev_{ISSUE}_{COMMENT}",
}));

const nanoSpawnModule = await import("nano-spawn");
const mockSpawn = vi.mocked(nanoSpawnModule.default);

describe("git-operations", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("hasChangesToCommit", () => {
    it("should return true when there are changes", async () => {
      mockSpawn.mockResolvedValueOnce({
        stdout: "M  file1.txt\nA  file2.txt\n",
        stderr: "",
      });

      const result = await gitOperations.hasChangesToCommit("/test/repo");
      expect(result).toBe(true);
      expect(mockSpawn).toHaveBeenCalledWith("git", ["status", "--porcelain"], {
        cwd: "/test/repo",
      });
    });

    it("should return false when there are no changes", async () => {
      mockSpawn.mockResolvedValueOnce({
        stdout: "",
        stderr: "",
      });

      const result = await gitOperations.hasChangesToCommit("/test/repo");
      expect(result).toBe(false);
    });

    it("should return false on error", async () => {
      mockSpawn.mockRejectedValueOnce(new Error("Git error"));

      const result = await gitOperations.hasChangesToCommit("/test/repo");
      expect(result).toBe(false);
    });
  });

  describe("addAllChanges", () => {
    it("should add all changes to staging area", async () => {
      mockSpawn.mockResolvedValueOnce({
        stdout: "",
        stderr: "",
      });

      await gitOperations.addAllChanges("/test/repo");
      expect(mockSpawn).toHaveBeenCalledWith("git", ["add", "."], {
        cwd: "/test/repo",
      });
    });
  });

  describe("commitChanges", () => {
    it("should commit changes with message", async () => {
      mockSpawn.mockResolvedValueOnce({
        stdout: "[main abc123] Test commit message",
        stderr: "",
      });

      await gitOperations.commitChanges("Test commit message", "/test/repo");
      expect(mockSpawn).toHaveBeenCalledWith(
        "git",
        ["commit", "-m", "Test commit message"],
        { cwd: "/test/repo" },
      );
    });
  });

  describe("pushBranch", () => {
    it("should push branch when remote exists", async () => {
      // Mock hasRemote to return true
      mockSpawn
        .mockResolvedValueOnce({
          stdout: "origin",
          stderr: "",
        })
        // Mock getCurrentBranch
        .mockResolvedValueOnce({
          stdout: "feature-branch",
          stderr: "",
        })
        // Mock push command
        .mockResolvedValueOnce({
          stdout: "Branch pushed successfully",
          stderr: "",
        });

      await gitOperations.pushBranch("/test/repo");

      expect(mockSpawn).toHaveBeenCalledWith(
        "git",
        ["push", "--set-upstream", "origin", "feature-branch"],
        { cwd: "/test/repo" },
      );
    });

    it("should skip push when no remote exists", async () => {
      // Mock hasRemote to return false
      mockSpawn.mockResolvedValueOnce({
        stdout: "",
        stderr: "",
      });

      const logCallback = vi.fn();
      await gitOperations.pushBranch("/test/repo", logCallback);

      expect(logCallback).toHaveBeenCalledWith(
        "Repository has no remote, skipping git push",
      );
    });
  });

  describe("commitAndPushChanges", () => {
    it("should commit and push when there are changes", async () => {
      // Mock hasChangesToCommit to return true
      mockSpawn
        .mockResolvedValueOnce({
          stdout: "M  file1.txt",
          stderr: "",
        })
        // Mock addAllChanges
        .mockResolvedValueOnce({
          stdout: "",
          stderr: "",
        })
        // Mock commitChanges
        .mockResolvedValueOnce({
          stdout:
            "[main abc123] AI task completion for TEST-123 (comment: comment-456)",
          stderr: "",
        })
        // Mock hasRemote for pushBranch
        .mockResolvedValueOnce({
          stdout: "origin",
          stderr: "",
        })
        // Mock getCurrentBranch for pushBranch
        .mockResolvedValueOnce({
          stdout: "feature-branch",
          stderr: "",
        })
        // Mock push command
        .mockResolvedValueOnce({
          stdout: "Branch pushed successfully",
          stderr: "",
        });

      await gitOperations.commitAndPushChanges(
        "TEST-123",
        "comment-456",
        "/test/repo",
      );

      expect(mockSpawn).toHaveBeenCalledWith(
        "git",
        [
          "commit",
          "-m",
          "AI task completion for TEST-123 (comment: comment-456)",
        ],
        { cwd: "/test/repo" },
      );
    });

    it("should skip when there are no changes", async () => {
      // Mock hasChangesToCommit to return false
      mockSpawn.mockResolvedValueOnce({
        stdout: "",
        stderr: "",
      });

      const logCallback = vi.fn();
      await gitOperations.commitAndPushChanges(
        "TEST-123",
        "comment-456",
        "/test/repo",
        logCallback,
      );

      expect(logCallback).toHaveBeenCalledWith("No changes to commit");
      // Should only call git status, not add/commit/push
      expect(mockSpawn).toHaveBeenCalledTimes(1);
    });
  });
});

import { NextRequest, NextResponse } from "next/server";
import * as taskQueue from "@/lib/task-queue";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ commentId: string }> },
) {
  try {
    const { commentId } = await params;

    if (!commentId) {
      return NextResponse.json(
        { error: "Comment ID is required" },
        { status: 400 },
      );
    }

    const tasks = taskQueue.getAllTasks();

    // Find the task by commentId
    const task = tasks.find((t) => t.commentId === commentId);

    if (!task) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 });
    }

    return Response.json({
      task,
      logs: task.logs,
      branch: task.branch,
    });
  } catch (error) {
    console.error("Error fetching task logs:", error);
    return NextResponse.json(
      { error: "Failed to fetch task logs" },
      { status: 500 },
    );
  }
}

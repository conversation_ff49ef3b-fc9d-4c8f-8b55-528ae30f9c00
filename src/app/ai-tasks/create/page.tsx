"use client";

import Link from "next/link";
import Form from "next/form";
import { useActionState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { createTask, type CreateTaskFormState } from "./actions";

export default function CreateAITaskPage() {
  const router = useRouter();
  const [state, formAction, isPending] = useActionState(
    createTask,
    {} as CreateTaskFormState,
  );

  // Handle redirect after successful task creation
  useEffect(() => {
    if (state.success && state.taskId) {
      router.push("/ai-tasks");
    }
  }, [state.success, state.taskId, router]);

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="mb-8">
        <Link
          href="/ai-tasks"
          className="text-blue-600 hover:text-blue-800 transition-colors"
        >
          &larr; Back to AI Tasks
        </Link>
      </div>

      <div className="bg-white shadow-md rounded-lg p-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-6">
          Create New AI Task
        </h1>

        <p className="text-gray-600 mb-6">
          Manually create a new AI task without syncing from Jira. Fill in all
          required fields below.
        </p>

        {state.error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            <strong className="font-bold">Error!</strong>
            <span className="block sm:inline"> {state.error}</span>
          </div>
        )}

        {state.success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
            <strong className="font-bold">Success!</strong>
            <span className="block sm:inline">
              {" "}
              Task created successfully. Redirecting...
            </span>
          </div>
        )}

        <Form action={formAction} className="space-y-6">
          <div>
            <label
              htmlFor="issueKey"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Issue Key <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="issueKey"
              name="issueKey"
              placeholder="e.g., JGCPS-123"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isPending}
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              The Jira issue key associated with this task
            </p>
          </div>

          <div>
            <label
              htmlFor="commentId"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Comment ID <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="commentId"
              name="commentId"
              placeholder="e.g., comment-12345"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isPending}
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              Unique identifier for this task (can be any unique string)
            </p>
          </div>

          <div>
            <label
              htmlFor="version"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Version <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="version"
              name="version"
              placeholder="e.g., v1.0.0 or feature-branch"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isPending}
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              Version or branch information for this task
            </p>
          </div>

          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Task Description <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              name="description"
              placeholder="Describe what this AI task should accomplish..."
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isPending}
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              Detailed description of what the AI should do
            </p>
          </div>

          <div>
            <label
              htmlFor="aiTool"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              AI Tool
            </label>
            <select
              id="aiTool"
              name="aiTool"
              defaultValue="aider"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isPending}
            >
              <option value="aider">Aider</option>
              <option value="openhands">OpenHands</option>
            </select>
            <p className="text-sm text-gray-500 mt-1">
              Choose which AI tool to use for this task
            </p>
          </div>

          <div className="flex gap-4 pt-4">
            <button
              type="submit"
              disabled={isPending}
              className={`flex-1 px-4 py-2 rounded text-white font-medium ${
                isPending
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700 transition-colors"
              }`}
            >
              {isPending ? (
                <span className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  Creating Task...
                </span>
              ) : (
                "Create AI Task"
              )}
            </button>

            <Link
              href="/ai-tasks"
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50 transition-colors text-center"
            >
              Cancel
            </Link>
          </div>
        </Form>
      </div>
    </div>
  );
}

import { notFound } from "next/navigation";
import * as taskQueue from "@/lib/task-queue";
import TaskLogsClient from "@/components/task-logs-client";
import { Task, TaskLog } from "@/types/task";

interface PageProperties {
  readonly params: Promise<{
    commentId: string;
  }>;
}

interface TaskLogsResponse {
  task: Task;
  logs: TaskLog[];
}

// Server component that fetches initial data
export default async function TaskLogsPage({ params }: PageProperties) {
  const { commentId } = await params;

  // Initialize task queue to ensure data is loaded
  taskQueue.initialize();

  // Find the task by commentId on the server
  const tasks = taskQueue.getAllTasks();
  const task = tasks.find((t) => t.commentId === commentId);

  if (!task) {
    notFound();
  }

  // Prepare initial data for the client component
  const initialData: TaskLogsResponse = {
    task,
    logs: task.logs,
  };

  // Pass initial data to client component
  return <TaskLogsClient initialData={initialData} commentId={commentId} />;
}

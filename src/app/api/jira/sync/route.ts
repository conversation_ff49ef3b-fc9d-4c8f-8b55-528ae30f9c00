import { NextResponse } from "next/server";
import * as jiraSync from "@/lib/jira-sync";

export async function POST() {
  try {
    await jiraSync.syncComments();

    return NextResponse.json({
      success: true,
      message: "Jira comments synced successfully",
      lastSync: jiraSync.getAIComments().lastSync,
    });
  } catch (error) {
    console.error("Error syncing <PERSON><PERSON> comments:", error);
    return NextResponse.json(
      { error: "Failed to sync Jira comments" },
      { status: 500 },
    );
  }
}

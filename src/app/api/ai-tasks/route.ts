import { NextRequest, NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";

import * as taskQueue from "@/lib/task-queue";

interface CreateTaskRequest {
  issueKey: string;
  commentId: string;
  version: string;
  description: string;
  aiTool?: "aider" | "openhands";
}

export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as CreateTaskRequest;
    const { issueKey, commentId, version, description, aiTool } = body;

    if (!issueKey || !commentId || !version || !description) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 },
      );
    }

    const task = taskQueue.addTask({
      id: uuidv4(),
      issueKey,
      commentId,
      version,
      description,
      aiTool: aiTool ?? "aider",
    });

    return NextResponse.json({
      success: true,
      task: {
        id: task.id,
        status: task.status,
        createdAt: task.createdAt,
      },
    });
  } catch (error) {
    console.error("Error creating task:", error);
    return NextResponse.json(
      { error: "Failed to create task" },
      { status: 500 },
    );
  }
}

export function GET() {
  try {
    const tasks = taskQueue.getAllTasks();

    return NextResponse.json({
      tasks: tasks.map((task) => ({
        id: task.id,
        issueKey: task.issueKey,
        commentId: task.commentId,
        description: task.description,
        version: task.version,
        status: task.status,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
        aiTool: task.aiTool,
        branch: task.branch,
      })),
    });
  } catch (error) {
    console.error("Error fetching tasks:", error);
    return NextResponse.json(
      { error: "Failed to fetch tasks" },
      { status: 500 },
    );
  }
}

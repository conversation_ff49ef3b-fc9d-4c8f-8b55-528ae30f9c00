"use client";

import { useState } from "react";
import Link from "next/link";

export default function AdminPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<
    | {
        success?: boolean;
        message?: string;
        error?: string;
        interval?: number;
      }
    | undefined
  >();

  const startCronJob = async () => {
    try {
      setIsLoading(true);
      setResult(undefined);

      const response = await fetch("/api/cron/jira-sync", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = (await response.json()) as {
        success?: boolean;
        message?: string;
        error?: string;
        interval?: number;
      };
      setResult(data);
    } catch {
      setResult({
        success: false,
        error: "Failed to start cron job. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <main className="flex min-h-screen flex-col items-center p-8">
      <div className="w-full max-w-4xl">
        <div className="mb-8">
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            &larr; Back to Home
          </Link>
        </div>

        <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>

        <div className="bg-white shadow-md rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Cron Job Management</h2>
          <p className="mb-4">
            Click the button below to manually trigger the Jira sync cron job.
            This will immediately start the synchronization process.
          </p>

          <button
            onClick={() => {
              void startCronJob();
            }}
            disabled={isLoading}
            className={`px-4 py-2 rounded text-white font-medium ${
              isLoading
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-blue-600 hover:bg-blue-700 transition-colors"
            }`}
          >
            {isLoading ? (
              <span className="flex items-center">
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                Processing...
              </span>
            ) : (
              "Start Jira Sync Now"
            )}
          </button>

          {result && (
            <div
              className={`mt-4 p-4 rounded ${
                result.success
                  ? "bg-green-50 border border-green-200 text-green-700"
                  : "bg-red-50 border border-red-200 text-red-700"
              }`}
            >
              {result.success ? (
                <>
                  <p className="font-medium">{result.message}</p>
                  {result.interval && (
                    <p className="text-sm mt-1">
                      Current sync interval: {result.interval} minutes
                    </p>
                  )}
                </>
              ) : (
                <p>{result.error}</p>
              )}
            </div>
          )}
        </div>
      </div>
    </main>
  );
}

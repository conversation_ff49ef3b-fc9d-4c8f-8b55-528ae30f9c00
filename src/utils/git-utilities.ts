/**
 * Pure utility functions for git operations
 * These functions don't have side effects and can be easily tested
 */

/**
 * Generate a branch name using a pattern and replacements
 */
export function generateBranchName(
  pattern: string,
  replacements: Record<string, string>,
): string {
  let result = pattern;

  for (const [key, value] of Object.entries(replacements)) {
    const placeholder = `{${key}}`;
    result = result.replaceAll(placeholder, value);
  }

  return result;
}

/**
 * Generate base branch name
 */
export function generateBaseBranchName(
  pattern: string,
  version: string,
): string {
  return generateBranchName(pattern, { VERSION: version });
}

/**
 * Generate issue branch name
 */
export function generateIssueBranchName(
  pattern: string,
  version: string,
  issueKey: string,
): string {
  return generateBranchName(pattern, {
    VERSION: version,
    ISSUE: issueKey,
  });
}

/**
 * Generate task branch name
 */
export function generateTaskBranchName(
  pattern: string,
  version: string,
  issueKey: string,
  commentId: string,
): string {
  return generateBranchName(pattern, {
    VERSION: version,
    ISSUE: issueKey,
    COMMENT: commentId,
  });
}

/**
 * Validate branch name format
 */
export function isValidBranchName(branchName: string): boolean {
  // Git branch name rules:
  // - Cannot start or end with a slash
  // - Cannot contain consecutive slashes
  // - Cannot contain spaces
  // - Cannot contain certain special characters
  const invalidChars = /[\s~^:?*[\]\\]/;
  const invalidPatterns = /(?:^\/|\/\/|\/$)/;

  if (!branchName || branchName.length === 0) {
    return false;
  }

  return !invalidChars.test(branchName) && !invalidPatterns.test(branchName);
}

/**
 * Sanitize a string to be safe for use in branch names
 */
export function sanitizeBranchName(input: string): string {
  let result = input
    .trim()
    .replaceAll(/[\s~^:?*[\]\\]/g, "-")
    .replaceAll(/\/+/g, "/");

  // Remove leading and trailing slashes
  while (result.startsWith("/")) {
    result = result.slice(1);
  }
  while (result.endsWith("/")) {
    result = result.slice(0, -1);
  }

  // Collapse multiple dashes and remove leading/trailing dashes
  result = result.replaceAll(/-+/g, "-");

  while (result.startsWith("-")) {
    result = result.slice(1);
  }
  while (result.endsWith("-")) {
    result = result.slice(0, -1);
  }

  return result;
}

/**
 * Extract version from fix versions array
 */
export function extractVersion(
  fixVersions: { name: string }[] | null | undefined,
  fallbackVersion = "3.11.1",
): string {
  return fixVersions?.[0]?.name ?? fallbackVersion;
}

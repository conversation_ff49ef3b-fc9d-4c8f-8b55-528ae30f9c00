# DevFlow AI

**AI-Powered Development Automation Platform**

DevFlow AI is a comprehensive automation platform that bridges Jira project management with AI-assisted code development. It monitors Jira issues for AI task requests, automatically executes them using advanced AI agents (Aider and OpenHands), and manages the entire development workflow from task creation to code delivery.

![Next.js](https://img.shields.io/badge/Next.js-15+-blue)
![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue)
![Docker](https://img.shields.io/badge/Docker-ready-blue)
![AI](https://img.shields.io/badge/AI-Aider%20%7C%20OpenHands-green)
![License](https://img.shields.io/badge/License-MIT-yellow)

## 🚀 Key Features

### 🤖 Multi-AI Agent Support

- **Aider**: Advanced AI coding assistant for precise code modifications
- **OpenHands**: Comprehensive AI development environment
- Configurable agent selection per task
- Extensible architecture for adding new AI agents

### 📋 Jira Integration

- **Smart Comment Parsing**: Automatically detects AI task requests in Jira comments
- **Flexible Authentication**: Supports multiple auth methods (API tokens, Basic auth, cookies)
- **Real-time Sync**: Configurable polling intervals for new tasks
- **Bidirectional Communication**: Posts task results and progress back to Jira

### 🔄 Git Workflow Automation

- **Intelligent Branch Management**: Automatically creates and manages feature branches
- **Configurable Patterns**: Customizable branch naming conventions
- **Version Control Integration**: Seamless integration with existing Git workflows
- **Safe Operations**: Built-in safeguards for repository operations

### 🎯 Task Management

- **Web-based Dashboard**: Intuitive interface for monitoring and managing tasks
- **Manual Task Creation**: Create tasks directly through the web interface
- **Real-time Status Tracking**: Live updates on task execution progress
- **Comprehensive Logging**: Detailed logs for debugging and audit trails

### 🔧 Enterprise-Ready

- **Docker Support**: Containerized deployment for easy scaling
- **Environment Configuration**: Comprehensive environment variable support
- **Security First**: Input sanitization and secure command execution
- **Monitoring & Observability**: Built-in logging and status reporting

## 🏗️ Architecture Overview

DevFlow AI follows a modern, scalable architecture built on Next.js 15+ with TypeScript:

### Core Components

- **Next.js App Router**: Server-side rendering with API routes
- **Pure Function Design**: Stateless, predictable business logic
- **File-based Persistence**: Simple, reliable data storage
- **Middleware Integration**: Automatic service initialization

### AI Agent Integration

- **Nano-spawn Process Management**: Secure, efficient process execution
- **Configurable Agent Parameters**: Flexible AI model and parameter configuration
- **Environment Isolation**: Safe execution environments for AI agents
- **Error Recovery**: Robust error handling and recovery mechanisms

### System Flow

For a detailed view of how the system processes AI tasks from Jira to Git, see the [AI Task Flow Diagram](docs/ai-task-flow-diagram.md). This comprehensive sequential diagram shows the complete interaction between Jira, the syncing server, AI agents, and Git remote repositories.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and pnpm (or npm/yarn)
- Docker (optional, for containerized deployment)
- Git repository access
- Jira instance with API access

### Installation

1. **Clone the repository:**

   ```bash
   git clone https://github.com/your-org/devflow-ai.git
   cd devflow-ai
   ```

2. **Install dependencies:**

   ```bash
   pnpm install
   ```

3. **Configure environment variables:**
   Create a `.env.local` file:

   ```bash
   # Repository Configuration
   REPO_PATH=/path/to/your/repository

   # Jira Integration
   JIRA_BASE_URL=https://your-instance.atlassian.net
   JIRA_API_TOKEN=your_api_token
   JIRA_JQL=project = YOUR_PROJECT AND updated >= -7d
   JIRA_SYNC_INTERVAL_MINUTES=5

   # AI Agent Configuration
   AIDER_ARCHITECT=true
   AIDER_MODEL=gpt-4
   OPENAI_API_KEY=your_openai_key
   OPENAI_BASE_URL=https://api.openai.com/v1

   # Application Settings
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   DATA_TASKS_DIR=data/tasks
   DATA_LOGS_DIR=data/logs
   ```

4. **Create data directories:**

   ```bash
   mkdir -p data/tasks data/logs
   ```

5. **Start the development server:**

   ```bash
   pnpm dev
   ```

6. **Access the application:**
   Open [http://localhost:3000](http://localhost:3000) in your browser.

## ⚙️ Configuration

### Environment Variables

DevFlow AI uses environment variables for configuration. Create a `.env.local` file with the following options:

#### Repository Settings

```bash
REPO_PATH=/path/to/your/repository  # Path to the Git repository
```

#### Jira Integration

```bash
JIRA_BASE_URL=https://your-instance.atlassian.net  # Jira instance URL
JIRA_JQL=project = YOUR_PROJECT AND updated >= -7d  # JQL query for task discovery

# Authentication (choose one method)
JIRA_API_TOKEN=your_api_token                    # API token (recommended)
JIRA_USERNAME=username                           # Basic auth username
JIRA_PASSWORD=password                           # Basic auth password
JIRA_AUTHORIZATION=base64_encoded_credentials    # Pre-encoded basic auth
JIRA_JSESSIONID=session_id                       # Session-based auth
JIRA_COOKIE=custom_cookie_string                 # Custom cookie auth

JIRA_SYNC_INTERVAL_MINUTES=5                     # Sync interval (default: 5)
```

#### AI Agent Configuration

```bash
# Aider Settings
AIDER_ARCHITECT=true                             # Enable architect mode
AIDER_MODEL=gpt-4                               # AI model for Aider
AIDER_EDITOR_MODEL=gpt-3.5-turbo               # Editor model for Aider

# OpenAI Configuration
OPENAI_API_KEY=your_openai_key                  # OpenAI API key
OPENAI_BASE_URL=https://api.openai.com/v1       # OpenAI API base URL
```

#### Application Settings

```bash
NEXT_PUBLIC_APP_URL=http://localhost:3000       # Public application URL
DATA_TASKS_DIR=data/tasks                       # Task storage directory
DATA_LOGS_DIR=data/logs                         # Log storage directory
```

#### Git Branch Patterns

```bash
GIT_BRANCH_PATTERN_BASE={VERSION}_dev           # Base branch pattern
GIT_BRANCH_PATTERN_ISSUE={VERSION}_dev_{ISSUE}  # Issue branch pattern
GIT_BRANCH_PATTERN_TASK={VERSION}_dev_{ISSUE}_{COMMENT}  # Task branch pattern
```

## 📝 Usage

### AI Task Format

Define AI tasks in Jira comments using this format:

````markdown
```ai-task
tool: aider  # Optional: aider (default) or openhands
description:
  Your task description here
  Multiple lines supported
  Be specific about what you want the AI to do
```
````

**Example:**

````markdown
```ai-task
tool: aider
description:
  Add input validation to the user registration form.
  Ensure email format is validated and passwords meet security requirements.
  Add appropriate error messages for invalid inputs.
```
````

### Manual Task Creation

1. Navigate to the DevFlow AI dashboard
2. Click "Create AI Task"
3. Fill in the required fields:
   - **Issue Key**: Jira issue identifier
   - **Comment ID**: Unique task identifier
   - **Version**: Version or branch information
   - **Description**: Detailed task instructions
   - **AI Tool**: Choose between Aider or OpenHands
4. Submit the task

### Monitoring Tasks

- **Dashboard**: View all tasks and their status
- **Task Details**: Click on any task to view detailed logs
- **Real-time Updates**: Task status updates automatically
- **Admin Panel**: Manage cron jobs and system settings

## 🔧 Development

### Local Development

```bash
# Start development server
pnpm dev

# Run linting
pnpm lint

# Run tests
pnpm test

# Check for unused code
pnpm knip
```

### Code Quality

- **ESLint**: Comprehensive linting with multiple plugins
- **Prettier**: Automatic code formatting
- **TypeScript**: Strict type checking
- **Husky**: Pre-commit hooks for quality assurance
- **Vitest**: Fast unit testing framework

## 🔄 API Endpoints

### Jira Sync

- **POST /api/jira/sync**
  - Manually trigger a Jira comment sync
  - Response: `{ success: true, message: "Jira comments synced successfully", lastSync: "2023-10-01T12:00:00Z" }`

### AI Tasks

- **GET /api/ai-tasks**

  - Get all tasks
  - Response: `{ tasks: [{ id, issueKey, commentId, status, createdAt, updatedAt }] }`

- **POST /api/ai-tasks**

  - Create a new task manually
  - Body: `{ issueKey, commentId, version, description, aiTool }`
  - Response: `{ success: true, task: { id, status, createdAt } }`

- **GET /api/ai-tasks/{commentId}/logs**
  - Get logs for a specific task
  - Response: `{ task: { id, issueKey, commentId, status, createdAt, updatedAt, branch }, logs: [{ timestamp, message, type }] }`

### Cron Jobs

- **POST /api/cron/jira-sync**

  - Restart the Jira sync cron job
  - Response: `{ success: true, message: "Jira sync job restarted successfully", interval: 5 }`

- **GET /api/cron/jira-sync**
  - Get the current Jira sync interval
  - Response: `{ success: true, interval: 5 }`

## 🐳 Docker Deployment

### Prerequisites

- [Docker](https://docs.docker.com/get-docker/) installed on your machine

### Building and Running

1. Build the Docker image:

   ```bash
   docker build -t jgcps-ai-tasks .
   ```

2. Run the container:

   ```bash
   docker run -p 3000:3000 \
     -e REPO_PATH=/app/repo \
     -e JIRA_BASE_URL=https://your-jira-instance.atlassian.net \
     -e JIRA_API_TOKEN=your-jira-api-token \
     -e JIRA_JQL="project = JGCPS AND updated >= -7d" \
     -e NEXT_PUBLIC_APP_URL=http://your-server:3000 \
     -v /path/to/local/repo:/app/repo \
     -v /path/to/data:/app/data \
     jgcps-ai-tasks
   ```

3. Access the application at http://localhost:3000

### Docker Compose (Optional)

Create a `docker-compose.yml` file:

```yaml
version: "3"
services:
  jgcps-ai-tasks:
    build: .
    ports:
      - "3000:3000"
    environment:
      - REPO_PATH=/app/repo
      - JIRA_BASE_URL=https://your-jira-instance.atlassian.net
      - JIRA_API_TOKEN=your-jira-api-token
      - JIRA_JQL="project = JGCPS AND updated >= -7d"
      - NEXT_PUBLIC_APP_URL=http://your-server:3000
    volumes:
      - /path/to/local/repo:/app/repo
      - /path/to/data:/app/data
```

Run with:

```bash
docker-compose up -d
```

## 📁 Project Structure

The project follows a modern Next.js application structure with a focus on modularity and separation of concerns:

```
jgcps-ai-tasks/
├── src/                      # Source code directory
│   ├── app/                  # Next.js App Router
│   │   ├── admin/            # Admin panel page
│   │   ├── ai-tasks/         # Task detail pages
│   │   │   └── [commentId]/  # Dynamic route for specific tasks
│   │   ├── api/              # API routes
│   │   │   ├── ai-tasks/     # Task management endpoints
│   │   │   │   └── [commentId]/logs/ # Task logs endpoint
│   │   │   ├── cron/         # Cron job management
│   │   │   ├── init/         # Server initialization endpoint
│   │   │   └── jira/sync/    # Jira synchronization endpoint
│   │   ├── layout.tsx        # Root layout component
│   │   └── page.tsx          # Home page
│   ├── config/               # Configuration from env vars
│   ├── lib/                  # Core business logic
│   │   ├── aiTaskExecutor.ts # AI task execution
│   │   ├── commentParser.ts  # Jira comment parsing
│   │   ├── cronJobs.ts       # Cron job management
│   │   ├── gitOperations.ts  # Git operations
│   │   ├── jiraSync.ts       # Jira synchronization
│   │   ├── serverInit.ts     # Server initialization
│   │   └── taskQueue.ts      # Task queue management
│   ├── services/             # External service integrations
│   │   └── jira.ts           # Jira API client
│   ├── styles/               # CSS styles
│   │   └── globals.css       # Global styles
│   ├── types/                # TypeScript type definitions
│   │   ├── jira.ts           # Jira-related types
│   │   └── task.ts           # Task-related types
│   └── middleware.ts         # Next.js middleware
├── data/                     # Data storage
│   ├── logs/                 # Task execution logs (JSON files)
│   └── tasks/                # Task data (JSON files)
├── Dockerfile                # Docker configuration
├── eslint.config.js          # ESLint configuration
├── knip.json                 # Knip configuration (unused code detection)
├── next.config.mjs           # Next.js configuration
├── package.json              # Project dependencies
├── postcss.config.mjs        # PostCSS configuration
├── tailwind.config.js        # Tailwind CSS configuration
└── tsconfig.json             # TypeScript configuration
```

### Key Directories and Files

- **src/app**: Contains all pages and API routes using Next.js App Router
- **src/lib**: Core business logic for AI task processing, git operations, and Jira integration
- **src/services**: External service integrations, currently just Jira
- **src/types**: TypeScript type definitions for better code quality
- **data**: Persistent storage for task data and execution logs
- **Dockerfile**: Container configuration for easy deployment

### Architecture Highlights

1. **API Routes**: RESTful endpoints for task management, Jira synchronization, and cron job control
2. **Task Processing**: Modular system for parsing AI tasks from Jira comments and executing them
3. **Git Integration**: Automated branch management following configurable patterns
4. **Data Persistence**: File-based storage for tasks and logs
5. **Server Initialization**: Middleware for setting up services on application start

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add my feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## 🐳 Docker Deployment

### Quick Docker Setup

```bash
# Build the image
docker build -t devflow-ai .

# Run with environment variables
docker run -p 3000:3000 \
  -e REPO_PATH=/app/repo \
  -e JIRA_BASE_URL=https://your-instance.atlassian.net \
  -e JIRA_API_TOKEN=your_token \
  -e OPENAI_API_KEY=your_key \
  -v /path/to/your/repo:/app/repo \
  -v /path/to/data:/app/data \
  devflow-ai
```

### Docker Compose

```yaml
version: "3.8"
services:
  devflow-ai:
    build: .
    ports:
      - "3000:3000"
    environment:
      - REPO_PATH=/app/repo
      - JIRA_BASE_URL=https://your-instance.atlassian.net
      - JIRA_API_TOKEN=your_token
      - JIRA_JQL=project = YOUR_PROJECT AND updated >= -7d
      - OPENAI_API_KEY=your_key
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
    volumes:
      - ./your-repo:/app/repo
      - ./data:/app/data
    restart: unless-stopped
```

## 🚀 Roadmap & Future Improvements

### Phase 1: Enhanced AI Integration

- [ ] **Multi-Model Support**: Add support for Claude, Gemini, and other AI models
- [ ] **Custom AI Agents**: Framework for creating custom AI agents for specific tasks
- [ ] **Agent Orchestration**: Ability to chain multiple AI agents for complex workflows
- [ ] **Smart Agent Selection**: Automatic agent selection based on task type and complexity

### Phase 2: Advanced Workflow Management

- [ ] **Workflow Templates**: Pre-defined workflows for common development patterns
- [ ] **Conditional Logic**: Support for if/then/else logic in task execution
- [ ] **Parallel Execution**: Run multiple tasks simultaneously with dependency management
- [ ] **Task Scheduling**: Cron-like scheduling for recurring tasks

### Phase 3: Enhanced Integrations

- [ ] **GitHub Integration**: Support for GitHub issues and pull requests
- [ ] **GitLab Support**: Extend to GitLab for broader Git platform support
- [ ] **Slack/Teams Notifications**: Real-time notifications for task completion
- [ ] **Webhook Support**: Custom webhooks for external system integration

### Phase 4: Enterprise Features

- [ ] **User Management**: Role-based access control and user permissions
- [ ] **Audit Logging**: Comprehensive audit trails for compliance
- [ ] **Multi-Tenant Support**: Support for multiple organizations/projects
- [ ] **SSO Integration**: Single sign-on with enterprise identity providers

### Phase 5: Advanced Analytics & Monitoring

- [ ] **Task Analytics**: Detailed metrics on task execution and success rates
- [ ] **Performance Monitoring**: Real-time monitoring of system performance
- [ ] **Cost Tracking**: Track AI usage costs and optimize spending
- [ ] **Predictive Analytics**: ML-powered insights for task optimization

### Phase 6: Developer Experience

- [ ] **VS Code Extension**: Direct integration with popular IDEs
- [ ] **CLI Tool**: Command-line interface for power users
- [ ] **API SDK**: Software development kits for popular languages
- [ ] **Plugin Architecture**: Extensible plugin system for custom functionality

### Phase 7: AI-Powered Enhancements

- [ ] **Intelligent Task Parsing**: Natural language understanding for task descriptions
- [ ] **Auto-Documentation**: Automatic generation of code documentation
- [ ] **Code Review Automation**: AI-powered code review and suggestions
- [ ] **Smart Conflict Resolution**: Automatic resolution of merge conflicts

## 🤝 Contributing

We welcome contributions from the community! Here's how you can help:

### Getting Started

1. Fork the repository
2. Create your feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Ensure all tests pass: `pnpm test`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

### Development Guidelines

- Follow the existing code style and conventions
- Write tests for new functionality
- Update documentation as needed
- Use conventional commits for clear history
- Ensure TypeScript strict mode compliance

### Areas for Contribution

- 🐛 **Bug Fixes**: Help identify and fix issues
- ✨ **New Features**: Implement items from the roadmap
- 📚 **Documentation**: Improve guides and API documentation
- 🧪 **Testing**: Add test coverage and improve test quality
- 🎨 **UI/UX**: Enhance the user interface and experience

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Aider](https://github.com/paul-gauthier/aider) - AI pair programming tool
- [OpenHands](https://github.com/All-Hands-AI/OpenHands) - AI software development platform
- [Next.js](https://nextjs.org/) - React framework for production
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework

---

**DevFlow AI** - Bridging the gap between project management and AI-assisted development. 🚀

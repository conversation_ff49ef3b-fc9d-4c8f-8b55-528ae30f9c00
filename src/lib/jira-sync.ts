import {
  AIComment,
  JiraIssue,
  Jira<PERSON>om<PERSON>,
  ParsedAIComment,
} from "@/types/jira";
import * as jiraService from "@/services/jira";
import { parseComment } from "./comment-parser";
import { v4 as uuidv4 } from "uuid";
import * as taskQueue from "./task-queue";
import { Task } from "@/types/task";
import { JIRA_JQL } from "@/config";

// Module state
let aiComments: AIComment[] = [];
let lastSync: Date | undefined;

/**
 * Sync comments from Jira
 */
export async function syncComments(): Promise<void> {
  try {
    // Get issues using the configured JQL query
    const issues = await jiraService.search({
      jql: JIRA_JQL,
      fields: ["summary", "status", "comment", "fixVersions"],
    });

    const syncResult = await processIssuesForAITasks(issues.issues);

    // Add new comments to the list
    aiComments = [...aiComments, ...syncResult.newComments];
    lastSync = new Date();

    // Log sync results
    logSyncResults(syncResult);
  } catch (error) {
    console.error("Error syncing comments:", error);
  }
}

/**
 * Process issues to find AI tasks
 */
async function processIssuesForAITasks(issues: JiraIssue[]): Promise<{
  newComments: AIComment[];
  existingTasksCount: number;
  totalAITasksFound: number;
}> {
  const newComments: AIComment[] = [];
  let existingTasksCount = 0;
  let totalAITasksFound = 0;

  for (const issue of issues) {
    const commentsResponse = await jiraService.getComments(issue.key);

    if (commentsResponse.comments) {
      const result = processCommentsForIssue(commentsResponse.comments, issue);
      newComments.push(...result.newComments);
      existingTasksCount += result.existingTasksCount;
      totalAITasksFound += result.totalAITasksFound;
    }
  }

  return { newComments, existingTasksCount, totalAITasksFound };
}

/**
 * Process comments for a single issue
 */
function processCommentsForIssue(
  comments: JiraComment[],
  issue: JiraIssue,
): {
  newComments: AIComment[];
  existingTasksCount: number;
  totalAITasksFound: number;
} {
  const newComments: AIComment[] = [];
  let existingTasksCount = 0;
  let totalAITasksFound = 0;

  for (const comment of comments) {
    const parsedComment = parseComment(comment, issue);

    if (parsedComment.isAITask) {
      totalAITasksFound++;
      // Check if we already have this comment
      const existingComment = aiComments.find(
        (c) => c.commentId === comment.id,
      );

      if (existingComment) {
        existingTasksCount++;
      } else {
        // This is a new AI task comment
        const aiComment = createAIComment(parsedComment, issue);
        newComments.push(aiComment);

        // Create a task for execution
        createTaskForExecution(aiComment);
      }
    }
  }

  return { newComments, existingTasksCount, totalAITasksFound };
}

/**
 * Log sync results
 */
function logSyncResults(result: {
  newComments: AIComment[];
  existingTasksCount: number;
  totalAITasksFound: number;
}): void {
  const { newComments, existingTasksCount, totalAITasksFound } = result;

  if (totalAITasksFound === 0) {
    console.log("No AI tasks found during sync");
  } else {
    if (existingTasksCount > 0) {
      console.log(
        `Found ${String(existingTasksCount)} existing AI task(s) during sync`,
      );
    }

    if (newComments.length > 0) {
      console.log(
        `Found ${String(newComments.length)} new AI task(s) during sync`,
      );
    } else {
      console.log("No new AI tasks found during sync");
    }
  }
}

/**
 * Create an AI comment from a parsed comment
 */
function createAIComment(
  parsedComment: ParsedAIComment,
  issue: JiraIssue,
): AIComment {
  const { taskInfo, originalComment } = parsedComment;

  return {
    id: uuidv4(),
    commentId: originalComment.id,
    issueKey: issue.key,
    comment: originalComment.body,
    content: taskInfo?.taskDescription ?? "",
    created: originalComment.created,
    author: originalComment.author.displayName,
    type: "task",
    issueSummary: issue.fields.summary,
    issueStatus: issue.fields.status.name,
    taskInfo: taskInfo
      ? {
          version: taskInfo.version,
          description: taskInfo.taskDescription,
          aiTool: taskInfo.aiTool,
          branch: taskInfo.branch,
        }
      : undefined,
  };
}

/**
 * Create a task for execution
 */
function createTaskForExecution(aiComment: AIComment): void {
  if (!aiComment.taskInfo) return;

  try {
    // Check if a task already exists for this comment
    const existingTasks = taskQueue.getAllTasks();
    const existingTask = existingTasks.find(
      (t: Task) => t.commentId === aiComment.commentId,
    );

    if (existingTask) {
      console.log(`Task already exists for comment ${aiComment.commentId}`);
      return;
    }

    // Create a new task
    taskQueue.addTask({
      id: uuidv4(),
      issueKey: aiComment.issueKey,
      commentId: aiComment.commentId,
      version: aiComment.taskInfo.version,
      description: aiComment.taskInfo.description,
      aiTool: aiComment.taskInfo.aiTool,
    });

    console.log(`Created task for comment ${aiComment.commentId}`);
  } catch (error) {
    console.error(
      `Error creating task for comment ${aiComment.commentId}:`,
      error,
    );
  }
}

/**
 * Get all AI comments and the last sync time
 */
export function getAIComments(): {
  comments: AIComment[];
  lastSync: Date | null;
} {
  return {
    comments: aiComments,
    lastSync: lastSync ?? null,
  };
}

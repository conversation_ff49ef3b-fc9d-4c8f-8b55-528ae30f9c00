import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  JiraSearchRequest,
  JiraSearchResponse,
  JiraIssue,
  JiraComment,
  WorklogClass,
} from "../types/jira";
import {
  JIRA_BASE_URL,
  JIRA_API_TOKEN,
  JIRA_HOME,
  JIRA_USERNAME,
  JIRA_PASSWORD,
  JIRA_AUTHORIZATION,
  JIRA_JSESSIONID,
  JIRA_COOKIE,
} from "@/config";

/**
 * Get Jira authentication headers from environment variables
 */
function getJSON() {
  const headers = {
    /** @see https://developer.atlassian.com/server/jira/platform/cookie-based-authentication/ */
    "Content-Type": "application/json",
    Host: JIRA_HOME.split("://")[1],
    Origin: JIRA_HOME,
    Referer: JIRA_HOME,
  };

  // Determine authentication method based on available environment variables
  if (JIRA_AUTHORIZATION) {
    // Basic auth with authorization token
    return {
      headers: {
        ...headers,
        Authorization: `Basic ${JIRA_AUTHORIZATION}`,
      },
    };
  } else if (JIRA_USERNAME && JIRA_PASSWORD) {
    // Basic auth with username/password
    const credentials = `${JIRA_USERNAME}:${JIRA_PASSWORD}`;
    const encodedCredentials = Buffer.from(credentials).toString("base64");
    return {
      headers: {
        ...headers,
        Authorization: `Basic ${encodedCredentials}`,
      },
    };
  } else if (JIRA_COOKIE) {
    // Cookie-based auth with custom cookie
    return {
      headers: {
        ...headers,
        cookie: JIRA_COOKIE,
      },
    };
  } else if (JIRA_JSESSIONID) {
    // Cookie-based auth with JSESSIONID
    return {
      headers: {
        ...headers,
        cookie: `JSESSIONID=${JIRA_JSESSIONID}`,
      },
    };
  }

  // Fallback to API token
  return {
    headers: {
      Authorization: `Bearer ${JIRA_API_TOKEN}`,
      "Content-Type": "application/json",
    },
  };
}

// Configuration for Jira API
const jiraConfig = {
  baseUrl: JIRA_BASE_URL,
};

/**
 * Make a request to the Jira API
 */
async function request<T>(path: string, options: RequestInit = {}): Promise<T> {
  const authInfo = getJSON();

  // Create a new headers object to avoid TypeScript errors
  const headers = new Headers(options.headers);

  // Add auth headers
  for (const [key, value] of Object.entries(authInfo.headers)) {
    headers.set(key, value as string);
  }

  const response = await fetch(`${jiraConfig.baseUrl}${path}`, {
    ...options,
    headers,
  });

  if (!response.ok) {
    const error = (await response.json()) as JiraError;
    throw new Error(error.errorMessages.join(", ") || "An error occurred");
  }

  return response.json() as Promise<T>;
}

/**
 * Search for Jira issues
 */
export async function search(
  parameters: JiraSearchRequest,
): Promise<JiraSearchResponse> {
  return request<JiraSearchResponse>("/rest/api/2/search", {
    method: "POST",
    body: JSON.stringify(parameters),
  });
}

/**
 * Get a Jira issue by key
 */
export async function getIssue(issueKey: string): Promise<JiraIssue> {
  return request<JiraIssue>(`/rest/api/2/issue/${issueKey}`);
}

/**
 * Get comments for a Jira issue
 */
export async function getComments(issueKey: string): Promise<WorklogClass> {
  return request<WorklogClass>(`/rest/api/2/issue/${issueKey}/comment`);
}

/**
 * Add a comment to a Jira issue
 */
export async function addComment(
  issueKey: string,
  body: string,
): Promise<JiraComment> {
  return request<JiraComment>(`/rest/api/2/issue/${issueKey}/comment`, {
    method: "POST",
    body: JSON.stringify({ body }),
  });
}

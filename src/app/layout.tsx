import React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "../styles/globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "JGCPS AI Comments",
  description: "AI comments tracking for JGCPS project",
};

interface RootLayoutProperties {
  readonly children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProperties) {
  return (
    <html lang="en">
      <body className={inter.className}>{children}</body>
    </html>
  );
}

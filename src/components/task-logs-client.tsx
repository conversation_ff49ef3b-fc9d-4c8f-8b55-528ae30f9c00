"use client";

import { useEffect, useState, useCallback } from "react";
import Link from "next/link";
import { Task, TaskLog } from "@/types/task";

interface TaskLogsResponse {
  task: Task;
  logs: TaskLog[];
}

interface TaskLogsClientProperties {
  initialData: TaskLogsResponse;
  commentId: string;
}

const getStatusBadgeClass = (status: string): string => {
  switch (status) {
    case "completed": {
      return "bg-green-100 text-green-800";
    }
    case "running": {
      return "bg-blue-100 text-blue-800";
    }
    case "failed": {
      return "bg-red-100 text-red-800";
    }
    default: {
      return "bg-gray-100 text-gray-800";
    }
  }
};

const getLogTypeClass = (type: string): string => {
  switch (type) {
    case "error": {
      return "text-red-400";
    }
    case "command": {
      return "text-yellow-400";
    }
    default: {
      return "text-green-400";
    }
  }
};

export default function TaskLogsClient({
  initialData,
  commentId,
}: Readonly<TaskLogsClientProperties>) {
  const [data, setData] = useState<TaskLogsResponse>(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();
  const [refreshInterval, setRefreshInterval] = useState<
    NodeJS.Timeout | undefined
  >();

  const fetchLogs = async () => {
    if (!commentId) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/ai-tasks/${commentId}/logs`);

      if (!response.ok) {
        throw new Error("Failed to fetch task logs");
      }

      const data = (await response.json()) as TaskLogsResponse;
      setData(data);
      setError(undefined);
    } catch (error_) {
      setError(error_ as Error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchLogsCallback = useCallback(fetchLogs, [commentId]);

  useEffect(() => {
    // If the task is running, refresh logs every 5 seconds
    if (data.task.status === "running" && !refreshInterval) {
      const interval = globalThis.setInterval(() => {
        void fetchLogsCallback();
      }, 5000);
      setRefreshInterval(interval);
    } else if (data.task.status !== "running" && refreshInterval) {
      globalThis.clearInterval(refreshInterval);
      setRefreshInterval(undefined);
    }

    return () => {
      if (refreshInterval) {
        globalThis.clearInterval(refreshInterval);
      }
    };
  }, [data.task.status, refreshInterval, fetchLogsCallback]);

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link
            href="/ai-tasks"
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            &larr; Back to AI Tasks
          </Link>
        </div>
        <div
          className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative"
          role="alert"
        >
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error.message}</span>
        </div>
      </div>
    );
  }

  const { task, logs } = data;

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <Link
          href="/ai-tasks"
          className="text-blue-600 hover:text-blue-800 transition-colors"
        >
          &larr; Back to AI Tasks
        </Link>
      </div>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Task Details</h1>
        <button
          onClick={() => {
            void fetchLogsCallback();
          }}
          disabled={isLoading}
          className={`px-4 py-2 rounded text-white font-medium ${
            isLoading
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-blue-600 hover:bg-blue-700 transition-colors"
          }`}
        >
          {isLoading ? "Refreshing..." : "Refresh"}
        </button>
      </div>

      {/* Task Information */}
      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Task Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span className="font-medium text-gray-700">Task ID:</span>
            <span className="ml-2 font-mono text-sm">{task.id}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Issue Key:</span>
            <span className="ml-2">{task.issueKey}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Version:</span>
            <span className="ml-2 font-mono">{task.version}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Status:</span>
            <span
              className={`ml-2 px-2 py-1 rounded text-sm font-medium ${getStatusBadgeClass(
                task.status,
              )}`}
            >
              {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
            </span>
          </div>
          <div>
            <span className="font-medium text-gray-700">AI Tool:</span>
            <span className="ml-2">{task.aiTool ?? "N/A"}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Branch:</span>
            <span className="ml-2 font-mono">{task.branch ?? "-"}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Created:</span>
            <span className="ml-2">
              {new Date(task.createdAt).toLocaleString()}
            </span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Updated:</span>
            <span className="ml-2">
              {new Date(task.updatedAt).toLocaleString()}
            </span>
          </div>
        </div>
        <div className="mt-4">
          <span className="font-medium text-gray-700">Description:</span>
          <p className="mt-1 text-gray-900">{task.description}</p>
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Execution Logs</h2>

        {logs.length === 0 ? (
          <div className="text-center text-gray-500 py-4">
            No logs available yet.
          </div>
        ) : (
          <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-auto max-h-[600px]">
            {logs.map((log, index) => (
              <div key={index} className="mb-1">
                <span className="text-gray-400">
                  [{new Date(log.timestamp).toLocaleTimeString()}]
                </span>{" "}
                <pre
                  className={`${getLogTypeClass(log.type)} inline whitespace-pre-wrap`}
                >
                  {log.message}
                </pre>
              </div>
            ))}
          </div>
        )}

        {task.status === "running" && (
          <div className="mt-4 text-center text-sm text-gray-500">
            Logs are automatically refreshed every 5 seconds while the task is
            running.
          </div>
        )}
      </div>
    </div>
  );
}

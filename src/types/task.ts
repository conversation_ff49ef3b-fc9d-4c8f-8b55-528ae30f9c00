export interface TaskLog {
  timestamp: string;
  message: string;
  type: "info" | "error" | "command";
}

export interface Task {
  id: string;
  issueKey: string;
  commentId: string;
  description: string;
  version: string;
  status: TaskStatus;
  createdAt: string;
  updatedAt: string;
  logs: TaskLog[];
  // 使用 aiTool 来确定使用哪个AI代理
  aiTool?: "aider" | "openhands";
  // 新增 branch 字段
  branch?: string;
}

export type TaskStatus =
  | "pending" // Task is queued but not started
  | "running" // Task is currently running
  | "completed" // Task completed successfully
  | "failed" // Task failed
  | "cancelled"; // Task was cancelled

export interface TaskExecutionResult {
  success: boolean;
  logs: TaskLog[];
  branch?: string;
  error?: string;
}

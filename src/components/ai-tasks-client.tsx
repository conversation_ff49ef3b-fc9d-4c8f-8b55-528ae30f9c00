"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { Task } from "@/types/task";

interface TasksResponse {
  tasks: Task[];
}

interface AITasksClientProperties {
  initialTasks: Task[];
}

const getStatusBadge = (status: string) => {
  const baseClasses = "px-2 py-1 rounded text-sm font-medium";
  switch (status) {
    case "completed": {
      return `${baseClasses} bg-green-100 text-green-800`;
    }
    case "running": {
      return `${baseClasses} bg-blue-100 text-blue-800`;
    }
    case "failed": {
      return `${baseClasses} bg-red-100 text-red-800`;
    }
    case "pending": {
      return `${baseClasses} bg-yellow-100 text-yellow-800`;
    }
    case "cancelled": {
      return `${baseClasses} bg-gray-100 text-gray-800`;
    }
    default: {
      return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  }
};

const getAIToolBadge = (aiTool?: string): string => {
  if (!aiTool)
    return "px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800";
  const baseClasses = "px-2 py-1 rounded text-xs font-medium";
  switch (aiTool) {
    case "aider": {
      return `${baseClasses} bg-purple-100 text-purple-800`;
    }
    case "openhands": {
      return `${baseClasses} bg-orange-100 text-orange-800`;
    }
    default: {
      return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  }
};

export default function AITasksClient({
  initialTasks,
}: Readonly<AITasksClientProperties>) {
  const [tasks, setTasks] = useState<Task[]>(initialTasks);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();
  const [sortBy, setSortBy] = useState<"createdAt" | "updatedAt" | "status">(
    "createdAt",
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [refreshInterval, setRefreshInterval] = useState<
    NodeJS.Timeout | undefined
  >();

  const fetchTasks = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/ai-tasks");

      if (!response.ok) {
        throw new Error("Failed to fetch AI tasks");
      }

      const data = (await response.json()) as TasksResponse;
      setTasks(data.tasks);
      setError(undefined);
    } catch (error_) {
      setError(error_ as Error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Auto-refresh if there are running tasks
    const hasRunningTasks = tasks.some((task) => task.status === "running");

    if (hasRunningTasks && !refreshInterval) {
      const interval = globalThis.setInterval(() => {
        void fetchTasks();
      }, 10_000); // Refresh every 10 seconds
      setRefreshInterval(interval);
    } else if (!hasRunningTasks && refreshInterval) {
      globalThis.clearInterval(refreshInterval);
      setRefreshInterval(undefined);
    }

    return () => {
      if (refreshInterval) {
        globalThis.clearInterval(refreshInterval);
      }
    };
  }, [tasks, refreshInterval]);

  const filteredTasks = tasks.filter((task) => {
    if (filterStatus === "all") return true;
    return task.status === filterStatus;
  });

  const sortedTasks = [...filteredTasks].sort((a, b) => {
    let aValue: string | number;
    let bValue: string | number;

    if (sortBy === "updatedAt") {
      aValue = new Date(a.updatedAt).getTime();
      bValue = new Date(b.updatedAt).getTime();
    } else if (sortBy === "status") {
      aValue = a.status;
      bValue = b.status;
    } else {
      // Default to createdAt for both "createdAt" and any other value
      aValue = new Date(a.createdAt).getTime();
      bValue = new Date(b.createdAt).getTime();
    }

    if (sortOrder === "asc") {
      if (aValue < bValue) return -1;
      if (aValue > bValue) return 1;
      return 0;
    } else {
      if (aValue > bValue) return -1;
      if (aValue < bValue) return 1;
      return 0;
    }
  });

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            &larr; Back to Home
          </Link>
        </div>
        <div
          className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative"
          role="alert"
        >
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error.message}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="mb-8">
        <Link
          href="/"
          className="text-blue-600 hover:text-blue-800 transition-colors"
        >
          &larr; Back to Home
        </Link>
      </div>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">AI Tasks Overview</h1>
        <div className="flex gap-3">
          <Link
            href="/ai-tasks/create"
            className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors font-medium"
          >
            Create New Task
          </Link>
          <button
            onClick={() => {
              void fetchTasks();
            }}
            disabled={isLoading}
            className={`px-4 py-2 rounded text-white font-medium ${
              isLoading
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-blue-600 hover:bg-blue-700 transition-colors"
            }`}
          >
            {isLoading ? "Refreshing..." : "Refresh"}
          </button>
        </div>
      </div>

      {/* Summary Statistics */}
      {tasks.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <div className="bg-white shadow-md rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-gray-800">
              {tasks.length}
            </div>
            <div className="text-sm text-gray-600">Total Tasks</div>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {tasks.filter((t) => t.status === "running").length}
            </div>
            <div className="text-sm text-gray-600">Running</div>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {tasks.filter((t) => t.status === "completed").length}
            </div>
            <div className="text-sm text-gray-600">Completed</div>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {tasks.filter((t) => t.status === "failed").length}
            </div>
            <div className="text-sm text-gray-600">Failed</div>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {tasks.filter((t) => t.status === "pending").length}
            </div>
            <div className="text-sm text-gray-600">Pending</div>
          </div>
        </div>
      )}

      {/* Filters and Sorting */}
      <div className="bg-white shadow-md rounded-lg p-4 mb-6">
        <div className="flex flex-wrap gap-4 items-center">
          <div>
            <label
              htmlFor="filter-status"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Filter by Status:
            </label>
            <select
              id="filter-status"
              value={filterStatus}
              onChange={(event) => {
                setFilterStatus(event.target.value);
              }}
              className="border border-gray-300 rounded px-3 py-1 text-sm"
            >
              <option value="all">All</option>
              <option value="pending">Pending</option>
              <option value="running">Running</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <div>
            <label
              htmlFor="sort-by"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Sort by:
            </label>
            <select
              id="sort-by"
              value={sortBy}
              onChange={(event) => {
                setSortBy(
                  event.target.value as "createdAt" | "updatedAt" | "status",
                );
              }}
              className="border border-gray-300 rounded px-3 py-1 text-sm"
            >
              <option value="createdAt">Created Date</option>
              <option value="updatedAt">Updated Date</option>
              <option value="status">Status</option>
            </select>
          </div>

          <div>
            <label
              htmlFor="sort-order"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Order:
            </label>
            <select
              id="sort-order"
              value={sortOrder}
              onChange={(event) => {
                setSortOrder(event.target.value as "asc" | "desc");
              }}
              className="border border-gray-300 rounded px-3 py-1 text-sm"
            >
              <option value="desc">Descending</option>
              <option value="asc">Ascending</option>
            </select>
          </div>

          <div className="ml-auto">
            <div className="text-sm text-gray-600">
              Total: {filteredTasks.length} tasks
              {tasks.some((task) => task.status === "running") && (
                <span className="ml-2 text-blue-600">
                  (Auto-refreshing every 10s)
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tasks Table */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        {sortedTasks.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            {filterStatus === "all"
              ? "No AI tasks found."
              : `No ${filterStatus} tasks found.`}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Task ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Issue Key
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Version
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    AI Tool
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Branch
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Updated
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedTasks.map((task) => (
                  <tr key={task.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                      {task.id.slice(0, 8)}...
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {task.issueKey}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 max-w-xs">
                      <div className="truncate" title={task.description}>
                        {task.description}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                      {task.version}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={getStatusBadge(task.status)}>
                        {task.status.charAt(0).toUpperCase() +
                          task.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={getAIToolBadge(task.aiTool)}>
                        {task.aiTool ?? "N/A"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                      {task.branch ?? "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(task.createdAt).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(task.updatedAt).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Link
                        href={`/ai-tasks/${task.commentId}`}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        View Logs
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

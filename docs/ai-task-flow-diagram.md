# AI Task Flow Sequential Diagram

This document contains a Mermaid sequential diagram showing the complete flow of AI task processing in the system, including interactions between <PERSON><PERSON>, the syncing server, AI agents, and Git remote repository.

## System Components

- **Jira**: Issue tracking system where AI tasks are defined in comments
- **Syncing Server**: Next.js application that orchestrates the entire process
- **AI Agent**: Either Aider or OpenHands AI coding assistant
- **Git Remote**: Remote Git repository where code changes are pushed

## Flow Diagram

```mermaid
sequenceDiagram
    participant J as Jira
    participant S as Syncing Server
    participant A as AI Agent
    participant G as Git Remote

    Note over S: Cron Job (every 5 minutes)

    %% 1. Jira Sync Process
    S->>J: Search issues with JQL query
    J-->>S: Return issues with comments

    loop For each issue
        S->>J: Get comments for issue
        J-->>S: Return comments

        Note over S: Parse comments for ai-task blocks

        alt AI task found in comment
            Note over S: Create task in queue
            S->>S: Add task to processing queue
        end
    end

    %% 2. Task Processing
    Note over S: Task Queue Processing

    S->>S: Get next pending task
    S->>S: Update task status to "running"

    %% 3. Git Branch Setup
    Note over S: Git Branch Setup
    S->>G: git checkout {VERSION}_dev (base branch)
    G-->>S: Switch to base branch

    alt Remote exists
        S->>G: git pull -r
        G-->>S: Pull latest changes
    end

    alt Issue branch exists
        S->>G: git checkout {VERSION}_dev_{ISSUE}
        G-->>S: Switch to issue branch
        S->>G: git pull -r
        G-->>S: Pull latest changes
    else Issue branch doesn't exist
        S->>G: git checkout -b {VERSION}_dev_{ISSUE}
        G-->>S: Create and switch to issue branch
    end

    S->>G: git checkout -b {VERSION}_dev_{ISSUE}_{COMMENT}
    G-->>S: Create and switch to task branch

    %% 4. Post Initial Jira Comment
    S->>J: Post comment with task URL
    J-->>S: Comment posted

    %% 5. AI Agent Execution
    Note over S,A: AI Agent Execution

    alt Tool is aider
        S->>A: Execute aider with task description
        Note over A: Aider analyzes code and makes changes
        A-->>S: Return execution results
    else Tool is openhands
        S->>A: Execute OpenHands Docker container
        Note over A: OpenHands analyzes code and makes changes
        A-->>S: Return execution results
    end

    %% 6. Git Commit and Push
    Note over S: Git Operations

    S->>G: git status --porcelain
    G-->>S: Return changed files

    alt Changes exist
        S->>G: git add .
        G-->>S: Stage all changes

        S->>G: git commit -m "AI task completion for {ISSUE} (comment: {COMMENT})"
        G-->>S: Commit changes

        alt Remote exists
            S->>G: git push --set-upstream origin {BRANCH}
            G-->>S: Push changes to remote
        end
    else No changes
        Note over S: Skip commit and push
    end

    %% 7. Task Completion
    S->>S: Update task status to "completed"

    %% 8. Error Handling
    Note over S: Error scenarios handled gracefully
    Note over S: Git operations are non-blocking
    Note over S: Task continues processing next in queue

    %% 9. Manual Triggers
    Note over S: Manual triggers available via API
    Note over S: /api/jira/sync - Manual Jira sync
    Note over S: /api/cron/jira-sync - Restart cron job
    Note over S: /api/ai-tasks - Create task manually
```

## Key Features

### 1. Automated Sync Process

- Cron job runs every 5 minutes (configurable)
- Searches Jira using JQL query for recent issues
- Parses comments for `ai-task` code blocks
- Creates tasks automatically when new AI tasks are found

### 2. Git Branch Management

- Creates hierarchical branch structure: base → issue → task
- Handles both new and existing branches
- Pulls latest changes before starting work
- Supports repositories with or without remotes

### 3. AI Agent Integration

- Supports multiple AI tools (Aider, OpenHands)
- Configurable through environment variables
- Sanitizes input to prevent shell injection
- Captures and logs all output

### 4. Smart Git Operations

- Only commits when there are actual changes
- Uses descriptive commit messages with issue and comment IDs
- Pushes to remote with upstream tracking
- Non-blocking - failures don't stop task completion

### 5. Error Handling

- Graceful handling of network failures
- Git operation failures are logged but don't fail tasks
- Task queue continues processing even if individual tasks fail
- Comprehensive logging for debugging

### 6. Manual Controls

- Manual Jira sync via API endpoint
- Manual task creation through web interface
- Cron job restart capability
- Real-time task monitoring and logs

## Environment Configuration

The system is highly configurable through environment variables:

- **JIRA_JQL**: Custom JQL query for finding issues
- **JIRA_SYNC_INTERVAL_MINUTES**: Sync frequency
- **GIT*BRANCH_PATTERN*\***: Customizable branch naming patterns
- **AIDER\_\***: AI agent configuration
- **REPO_PATH**: Target repository path

This design ensures a robust, scalable, and maintainable AI-assisted development workflow.

import { describe, it, expect, vi } from "vitest";
import {
  sanitizeCommandInput,
  generateTimestamp,
  extractContentBetweenMarkers,
  parseKeyValueLines,
  joinNonEmpty,
  truncateText,
  camelToKebab,
  kebabToCamel,
  escapeRegex,
  isValidUrl,
} from "@/utils/string-utilities";

describe("string-utils", () => {
  describe("sanitizeCommandInput", () => {
    it("should remove YAML pipe symbols and newlines", () => {
      const input = "description: |\n  This is a task";
      const result = sanitizeCommandInput(input);
      expect(result).toBe("description:   This is a task");
    });

    it("should remove dangerous shell characters", () => {
      const input = 'command & echo "hello"; rm -rf /';
      const result = sanitizeCommandInput(input);
      expect(result).toBe("command  echo hello rm -rf /");
    });

    it("should handle multiple dangerous characters", () => {
      const input = "test & | < > $ ` \" ' \\ command";
      const result = sanitizeCommandInput(input);
      expect(result).toBe("test          command");
    });

    it("should trim whitespace", () => {
      const input = "  command with spaces  ";
      const result = sanitizeCommandInput(input);
      expect(result).toBe("command with spaces");
    });

    it("should handle empty string", () => {
      expect(sanitizeCommandInput("")).toBe("");
      expect(sanitizeCommandInput("   ")).toBe("");
    });

    it("should preserve safe characters", () => {
      const input = "safe-command_with.numbers123";
      const result = sanitizeCommandInput(input);
      expect(result).toBe("safe-command_with.numbers123");
    });
  });

  describe("generateTimestamp", () => {
    it("should generate timestamp in correct format", () => {
      // Mock Date to return a fixed timestamp
      const mockDate = new Date("2023-10-15T14:30:45.123Z");
      vi.spyOn(global, "Date").mockImplementation(() => mockDate);

      const result = generateTimestamp();
      expect(result).toBe("20231015143045");
      expect(result).toHaveLength(14);

      vi.restoreAllMocks();
    });

    it("should only contain digits", () => {
      const result = generateTimestamp();
      expect(result).toMatch(/^\d{14}$/);
    });
  });

  describe("extractContentBetweenMarkers", () => {
    it("should extract content between markers", () => {
      const text = "before ```ai-task\ncontent here\n``` after";
      const result = extractContentBetweenMarkers(text, "```ai-task", "```");
      expect(result).toBe("content here");
    });

    it("should return undefined when start marker not found", () => {
      const text = "no markers here";
      const result = extractContentBetweenMarkers(text, "```ai-task", "```");
      expect(result).toBeUndefined();
    });

    it("should return undefined when end marker not found", () => {
      const text = "before ```ai-task content without end";
      const result = extractContentBetweenMarkers(text, "```ai-task", "```");
      expect(result).toBeUndefined();
    });

    it("should handle empty content between markers", () => {
      const text = "before ```ai-task``` after";
      const result = extractContentBetweenMarkers(text, "```ai-task", "```");
      expect(result).toBe("");
    });

    it("should find first occurrence of markers", () => {
      const text =
        "before ```ai-task first ``` middle ```ai-task second ``` after";
      const result = extractContentBetweenMarkers(text, "```ai-task", "```");
      expect(result).toBe("first");
    });

    it("should trim whitespace from extracted content", () => {
      const text = "before ```ai-task\n  content with spaces  \n``` after";
      const result = extractContentBetweenMarkers(text, "```ai-task", "```");
      expect(result).toBe("content with spaces");
    });
  });

  describe("parseKeyValueLines", () => {
    it("should parse key-value pairs with default separator", () => {
      const lines = ["key1: value1", "key2: value2", "key3: value3"];
      const result = parseKeyValueLines(lines);
      expect(result).toEqual({
        key1: "value1",
        key2: "value2",
        key3: "value3",
      });
    });

    it("should parse key-value pairs with custom separator", () => {
      const lines = ["key1=value1", "key2=value2"];
      const result = parseKeyValueLines(lines, "=");
      expect(result).toEqual({
        key1: "value1",
        key2: "value2",
      });
    });

    it("should handle lines without separator", () => {
      const lines = ["key1: value1", "invalid line", "key2: value2"];
      const result = parseKeyValueLines(lines);
      expect(result).toEqual({
        key1: "value1",
        key2: "value2",
      });
    });

    it("should trim whitespace from keys and values", () => {
      const lines = ["  key1  :  value1  ", "key2:value2"];
      const result = parseKeyValueLines(lines);
      expect(result).toEqual({
        key1: "value1",
        key2: "value2",
      });
    });

    it("should handle empty lines", () => {
      const lines = ["key1: value1", "", "   ", "key2: value2"];
      const result = parseKeyValueLines(lines);
      expect(result).toEqual({
        key1: "value1",
        key2: "value2",
      });
    });

    it("should handle values with separator characters", () => {
      const lines = ["url: https://example.com:8080"];
      const result = parseKeyValueLines(lines);
      expect(result).toEqual({
        url: "https://example.com:8080",
      });
    });
  });

  describe("joinNonEmpty", () => {
    it("should join non-empty strings with default separator", () => {
      const strings = ["hello", "world", "test"];
      const result = joinNonEmpty(strings);
      expect(result).toBe("hello world test");
    });

    it("should join non-empty strings with custom separator", () => {
      const strings = ["hello", "world", "test"];
      const result = joinNonEmpty(strings, ", ");
      expect(result).toBe("hello, world, test");
    });

    it("should filter out empty strings", () => {
      const strings = ["hello", "", "world", "   ", "test"];
      const result = joinNonEmpty(strings);
      expect(result).toBe("hello world test");
    });

    it("should handle all empty strings", () => {
      const strings = ["", "   ", ""];
      const result = joinNonEmpty(strings);
      expect(result).toBe("");
    });

    it("should handle empty array", () => {
      const result = joinNonEmpty([]);
      expect(result).toBe("");
    });
  });

  describe("truncateText", () => {
    it("should not truncate text shorter than max length", () => {
      const text = "short text";
      const result = truncateText(text, 20);
      expect(result).toBe("short text");
    });

    it("should truncate text longer than max length", () => {
      const text = "this is a very long text that should be truncated";
      const result = truncateText(text, 20);
      expect(result).toBe("this is a very lo...");
      expect(result).toHaveLength(20);
    });

    it("should handle max length equal to text length", () => {
      const text = "exact length";
      const result = truncateText(text, 12);
      expect(result).toBe("exact length");
    });

    it("should handle very short max length", () => {
      const text = "hello";
      const result = truncateText(text, 3);
      expect(result).toBe("...");
    });
  });

  describe("camelToKebab", () => {
    it("should convert camelCase to kebab-case", () => {
      expect(camelToKebab("camelCase")).toBe("camel-case");
      expect(camelToKebab("someVariableName")).toBe("some-variable-name");
      expect(camelToKebab("HTMLElement")).toBe("html-element");
    });

    it("should handle single words", () => {
      expect(camelToKebab("word")).toBe("word");
      expect(camelToKebab("Word")).toBe("word");
    });

    it("should handle numbers", () => {
      expect(camelToKebab("version2Update")).toBe("version2-update");
      expect(camelToKebab("test123Value")).toBe("test123-value");
    });

    it("should handle empty string", () => {
      expect(camelToKebab("")).toBe("");
    });
  });

  describe("kebabToCamel", () => {
    it("should convert kebab-case to camelCase", () => {
      expect(kebabToCamel("kebab-case")).toBe("kebabCase");
      expect(kebabToCamel("some-variable-name")).toBe("someVariableName");
      expect(kebabToCamel("multi-word-string")).toBe("multiWordString");
    });

    it("should handle single words", () => {
      expect(kebabToCamel("word")).toBe("word");
    });

    it("should handle strings without dashes", () => {
      expect(kebabToCamel("nodashes")).toBe("nodashes");
    });

    it("should handle empty string", () => {
      expect(kebabToCamel("")).toBe("");
    });
  });

  describe("escapeRegex", () => {
    it("should escape special regex characters", () => {
      expect(escapeRegex(".*+?^${}()|[]\\")).toBe(
        "\\.\\*\\+\\?\\^\\$\\{\\}\\(\\)\\|\\[\\]\\\\",
      );
    });

    it("should not escape normal characters", () => {
      expect(escapeRegex("normal text 123")).toBe("normal text 123");
    });

    it("should handle mixed content", () => {
      expect(escapeRegex("test.regex*pattern")).toBe(
        String.raw`test\.regex\*pattern`,
      );
    });

    it("should handle empty string", () => {
      expect(escapeRegex("")).toBe("");
    });
  });

  describe("isValidUrl", () => {
    it("should return true for valid URLs", () => {
      expect(isValidUrl("https://example.com")).toBe(true);
      expect(isValidUrl("http://localhost:3000")).toBe(true);
      expect(isValidUrl("ftp://files.example.com")).toBe(true);
      expect(isValidUrl("https://api.example.com/v1/users")).toBe(true);
    });

    it("should return false for invalid URLs", () => {
      expect(isValidUrl("not a url")).toBe(false);
      expect(isValidUrl("example.com")).toBe(false);
      expect(isValidUrl("http://")).toBe(false);
      expect(isValidUrl("")).toBe(false);
    });

    it("should handle edge cases", () => {
      // Note: javascript: URLs are technically valid but potentially unsafe
      expect(isValidUrl("data:text/plain;base64,SGVsbG8=")).toBe(true);
      expect(isValidUrl("ftp://example.com")).toBe(true);
    });
  });
});

import "@testing-library/jest-dom";

// Mock environment variables for testing
process.env.NODE_ENV = "test";
process.env.REPO_PATH = "/test/repo";
process.env.JIRA_BASE_URL = "https://test.atlassian.net";
process.env.DATA_TASKS_DIR = "test/data/tasks";
process.env.DATA_LOGS_DIR = "test/data/logs";
process.env.GIT_BRANCH_PATTERN_BASE = "{VERSION}_dev";
process.env.GIT_BRANCH_PATTERN_ISSUE = "{VERSION}_dev_{ISSUE}";
process.env.GIT_BRANCH_PATTERN_TASK = "{VERSION}_dev_{ISSUE}_{COMMENT}";

// Global test utilities can be added here

import { describe, it, expect } from "vitest";
import {
  generateBranchName,
  generateBaseBranchName,
  generateIssueBranchName,
  generateTaskBranchName,
  isValidBranchName,
  sanitizeBranchName,
  extractVersion,
} from "@/utils/git-utilities";

describe("git-utils", () => {
  describe("generateBranchName", () => {
    it("should replace single placeholder", () => {
      const result = generateBranchName("{VERSION}_dev", { VERSION: "3.11.1" });
      expect(result).toBe("3.11.1_dev");
    });

    it("should replace multiple placeholders", () => {
      const result = generateBranchName("{VERSION}_dev_{ISSUE}_{COMMENT}", {
        VERSION: "3.11.1",
        ISSUE: "TEST-123",
        COMMENT: "comment-456",
      });
      expect(result).toBe("3.11.1_dev_TEST-123_comment-456");
    });

    it("should handle missing placeholders gracefully", () => {
      const result = generateBranchName("{VERSION}_dev_{MISSING}", {
        VERSION: "3.11.1",
      });
      expect(result).toBe("3.11.1_dev_{MISSING}");
    });

    it("should handle empty pattern", () => {
      const result = generateBranchName("", { VERSION: "3.11.1" });
      expect(result).toBe("");
    });

    it("should handle empty replacements", () => {
      const result = generateBranchName("{VERSION}_dev", {});
      expect(result).toBe("{VERSION}_dev");
    });

    it("should replace all occurrences of the same placeholder", () => {
      const result = generateBranchName("{VERSION}_{VERSION}_dev", {
        VERSION: "3.11.1",
      });
      expect(result).toBe("3.11.1_3.11.1_dev");
    });
  });

  describe("generateBaseBranchName", () => {
    it("should generate base branch name correctly", () => {
      const result = generateBaseBranchName("{VERSION}_dev", "3.11.1");
      expect(result).toBe("3.11.1_dev");
    });

    it("should handle different patterns", () => {
      const result = generateBaseBranchName("release-{VERSION}", "3.11.1");
      expect(result).toBe("release-3.11.1");
    });
  });

  describe("generateIssueBranchName", () => {
    it("should generate issue branch name correctly", () => {
      const result = generateIssueBranchName(
        "{VERSION}_dev_{ISSUE}",
        "3.11.1",
        "TEST-123",
      );
      expect(result).toBe("3.11.1_dev_TEST-123");
    });

    it("should handle different patterns", () => {
      const result = generateIssueBranchName(
        "feature/{ISSUE}-{VERSION}",
        "3.11.1",
        "TEST-123",
      );
      expect(result).toBe("feature/TEST-123-3.11.1");
    });
  });

  describe("generateTaskBranchName", () => {
    it("should generate task branch name correctly", () => {
      const result = generateTaskBranchName(
        "{VERSION}_dev_{ISSUE}_{COMMENT}",
        "3.11.1",
        "TEST-123",
        "comment-456",
      );
      expect(result).toBe("3.11.1_dev_TEST-123_comment-456");
    });

    it("should handle different patterns", () => {
      const result = generateTaskBranchName(
        "task/{ISSUE}/{COMMENT}-{VERSION}",
        "3.11.1",
        "TEST-123",
        "comment-456",
      );
      expect(result).toBe("task/TEST-123/comment-456-3.11.1");
    });
  });

  describe("isValidBranchName", () => {
    it("should return true for valid branch names", () => {
      expect(isValidBranchName("main")).toBe(true);
      expect(isValidBranchName("feature/test")).toBe(true);
      expect(isValidBranchName("3.11.1_dev_TEST-123")).toBe(true);
      expect(isValidBranchName("hotfix-urgent")).toBe(true);
    });

    it("should return false for invalid branch names", () => {
      expect(isValidBranchName("")).toBe(false);
      expect(isValidBranchName("branch with spaces")).toBe(false);
      expect(isValidBranchName("branch~with~tildes")).toBe(false);
      expect(isValidBranchName("branch^with^carets")).toBe(false);
      expect(isValidBranchName("branch:with:colons")).toBe(false);
      expect(isValidBranchName("branch?with?questions")).toBe(false);
      expect(isValidBranchName("branch*with*asterisks")).toBe(false);
      expect(isValidBranchName("branch[with]brackets")).toBe(false);
      expect(isValidBranchName(String.raw`branch\with\backslashes`)).toBe(
        false,
      );
    });

    it("should return false for branch names with invalid patterns", () => {
      expect(isValidBranchName("/starts-with-slash")).toBe(false);
      expect(isValidBranchName("ends-with-slash/")).toBe(false);
      expect(isValidBranchName("has//double//slashes")).toBe(false);
    });

    it("should handle null and undefined", () => {
      expect(isValidBranchName(null as unknown as string)).toBe(false);
      expect(isValidBranchName(undefined as unknown as string)).toBe(false);
    });
  });

  describe("sanitizeBranchName", () => {
    it("should sanitize invalid characters", () => {
      expect(sanitizeBranchName("branch with spaces")).toBe(
        "branch-with-spaces",
      );
      expect(sanitizeBranchName("branch~with~tildes")).toBe(
        "branch-with-tildes",
      );
      expect(sanitizeBranchName("branch:with:colons")).toBe(
        "branch-with-colons",
      );
    });

    it("should handle multiple consecutive invalid characters", () => {
      expect(sanitizeBranchName("branch   with   spaces")).toBe(
        "branch-with-spaces",
      );
      expect(sanitizeBranchName("branch~~~tildes")).toBe("branch-tildes");
    });

    it("should remove leading and trailing slashes", () => {
      expect(sanitizeBranchName("/branch/")).toBe("branch");
      expect(sanitizeBranchName("///branch///")).toBe("branch");
    });

    it("should handle consecutive slashes", () => {
      expect(sanitizeBranchName("feature//branch")).toBe("feature/branch");
      expect(sanitizeBranchName("feature///branch")).toBe("feature/branch");
    });

    it("should trim whitespace", () => {
      expect(sanitizeBranchName("  branch  ")).toBe("branch");
    });

    it("should handle empty string", () => {
      expect(sanitizeBranchName("")).toBe("");
      expect(sanitizeBranchName("   ")).toBe("");
    });

    it("should handle complex cases", () => {
      expect(sanitizeBranchName("  /feature: test~branch*  /")).toBe(
        "feature-test-branch",
      );
    });
  });

  describe("extractVersion", () => {
    it("should extract version from first fix version", () => {
      const fixVersions = [{ name: "3.11.1" }, { name: "3.11.2" }];
      expect(extractVersion(fixVersions)).toBe("3.11.1");
    });

    it("should return fallback version for empty array", () => {
      expect(extractVersion([])).toBe("3.11.1");
      expect(extractVersion([], "custom-fallback")).toBe("custom-fallback");
    });

    it("should handle undefined/null fix versions", () => {
      expect(extractVersion(null as unknown as { name: string }[])).toBe(
        "3.11.1",
      );
      expect(extractVersion(undefined as unknown as { name: string }[])).toBe(
        "3.11.1",
      );
    });

    it("should handle fix versions with undefined names", () => {
      const fixVersions = [
        { name: undefined as unknown as string },
        { name: "3.11.2" },
      ];
      expect(extractVersion(fixVersions)).toBe("3.11.1");
    });

    it("should use custom fallback version", () => {
      expect(extractVersion([], "4.0.0")).toBe("4.0.0");
    });
  });
});
